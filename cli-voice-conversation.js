#!/usr/bin/env node

// CLI Voice Conversation Test
// Interactive voice conversation with Qala-Lwazi medical assistant

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const BASE_URL = 'http://localhost:3001';

class VoiceConversationCLI {
  constructor() {
    this.sessionId = `voice_cli_${Date.now()}`;
    this.conversationHistory = [];
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // Display welcome message
  displayWelcome() {
    console.log('\n🎤 QALA-LWAZI VOICE CONVERSATION TEST');
    console.log('=====================================');
    console.log('Welcome to the interactive voice conversation test!');
    console.log('This will test both speech-to-text and text-to-speech capabilities.\n');
    console.log('Available commands:');
    console.log('  - Type your message to simulate speech input');
    console.log('  - Type "voice" to test actual voice input (if audio file available)');
    console.log('  - Type "tts <text>" to test text-to-speech only');
    console.log('  - Type "history" to see conversation history');
    console.log('  - Type "clear" to clear conversation');
    console.log('  - Type "quit" to exit\n');
  }

  // Test text-to-speech functionality
  async testTextToSpeech(text, language = 'english') {
    try {
      console.log(`🔊 Converting to speech: "${text.substring(0, 50)}..."`);
      
      const response = await axios.post(`${BASE_URL}/api/text-to-speech`, {
        text: text,
        language: language
      });

      if (response.data.success) {
        console.log(`✅ TTS Success! Generated ${response.data.duration}s of audio`);
        console.log(`   Format: ${response.data.audioFormat}`);
        console.log(`   Method: ${response.data.method || 'gemini-tts'}`);
        
        if (response.data.note) {
          console.log(`   Note: ${response.data.note}`);
        }
        
        return true;
      } else {
        console.log('❌ TTS Failed');
        return false;
      }
    } catch (error) {
      console.error('❌ TTS Error:', error.response?.data?.error || error.message);
      return false;
    }
  }

  // Test speech-to-text with mock audio
  async testSpeechToText(mockText, language = 'english') {
    try {
      console.log(`🎤 Simulating speech input: "${mockText}"`);
      
      // Create a mock audio buffer for testing
      const mockAudioBuffer = Buffer.from('mock-audio-data-' + mockText);
      
      // Save as temporary file
      const tempFile = path.join(__dirname, 'temp_mock_audio.wav');
      fs.writeFileSync(tempFile, mockAudioBuffer);
      
      // Create form data
      const FormData = require('form-data');
      const form = new FormData();
      form.append('audio', fs.createReadStream(tempFile));
      form.append('language', language);
      
      const response = await axios.post(`${BASE_URL}/api/speech-to-text`, form, {
        headers: form.getHeaders()
      });

      // Clean up temp file
      fs.unlinkSync(tempFile);

      if (response.data.success) {
        console.log(`✅ STT Success! Transcription: "${response.data.transcription}"`);
        console.log(`   Confidence: ${(response.data.confidence * 100).toFixed(1)}%`);
        console.log(`   Method: ${response.data.method || 'gemini-stt'}`);
        return response.data.transcription;
      } else {
        console.log('❌ STT Failed');
        return null;
      }
    } catch (error) {
      console.error('❌ STT Error:', error.response?.data?.error || error.message);
      return null;
    }
  }

  // Send medical chat message
  async sendMedicalMessage(message, language = 'english') {
    try {
      console.log(`🧠 Processing medical query: "${message}"`);
      
      const response = await axios.post(`${BASE_URL}/api/medical-chat`, {
        message: message,
        sessionId: this.sessionId,
        userPreferences: {
          useRAG: true,
          detailLevel: 'medium'
        }
      });

      if (response.data.response) {
        const aiResponse = response.data.response;
        console.log(`\n🤖 QALA-LWAZI RESPONSE:`);
        console.log(`${aiResponse}\n`);
        
        console.log(`📊 Response Info:`);
        console.log(`   Length: ${response.data.responseLength} characters`);
        console.log(`   RAG Used: ${response.data.usingRAG}`);
        console.log(`   Language: ${response.data.detectedLanguage} -> ${response.data.sessionLanguage}`);
        console.log(`   Medical Mode: ${response.data.medicalMode}`);
        if (response.data.womensHealthMode) {
          console.log(`   👩‍⚕️ Women's Health Mode: Activated`);
        }
        
        // Add to conversation history
        this.conversationHistory.push({
          user: message,
          assistant: aiResponse,
          timestamp: new Date().toISOString(),
          metadata: {
            ragUsed: response.data.usingRAG,
            language: response.data.sessionLanguage,
            medicalMode: response.data.medicalMode
          }
        });
        
        return aiResponse;
      } else {
        console.log('❌ No response received');
        return null;
      }
    } catch (error) {
      console.error('❌ Medical Chat Error:', error.response?.data?.error || error.message);
      return null;
    }
  }

  // Full voice conversation simulation
  async simulateVoiceConversation(userInput, language = 'english') {
    console.log('\n🎙️ SIMULATING FULL VOICE CONVERSATION');
    console.log('=====================================');
    
    // Step 1: Simulate speech-to-text
    console.log('Step 1: Speech Recognition');
    const transcription = await this.testSpeechToText(userInput, language);
    
    if (!transcription) {
      console.log('❌ Voice conversation failed at STT step');
      return;
    }
    
    // Step 2: Process medical query
    console.log('\nStep 2: Medical Processing');
    const aiResponse = await this.sendMedicalMessage(transcription, language);
    
    if (!aiResponse) {
      console.log('❌ Voice conversation failed at medical processing step');
      return;
    }
    
    // Step 3: Convert response to speech
    console.log('\nStep 3: Text-to-Speech');
    const ttsSuccess = await this.testTextToSpeech(aiResponse, language);
    
    if (ttsSuccess) {
      console.log('\n✅ FULL VOICE CONVERSATION COMPLETED SUCCESSFULLY!');
      console.log('🔄 The AI would now speak the response to you.');
    } else {
      console.log('\n⚠️ Voice conversation completed but TTS failed');
    }
  }

  // Display conversation history
  displayHistory() {
    console.log('\n📚 CONVERSATION HISTORY');
    console.log('=======================');
    
    if (this.conversationHistory.length === 0) {
      console.log('No conversation history yet.');
      return;
    }
    
    this.conversationHistory.forEach((exchange, index) => {
      console.log(`\n--- Exchange ${index + 1} ---`);
      console.log(`👤 User: ${exchange.user}`);
      console.log(`🤖 Assistant: ${exchange.assistant.substring(0, 200)}...`);
      console.log(`📊 Info: RAG=${exchange.metadata.ragUsed}, Lang=${exchange.metadata.language}, Mode=${exchange.metadata.medicalMode}`);
    });
  }

  // Clear conversation
  async clearConversation() {
    try {
      await axios.delete(`${BASE_URL}/api/chat-history/${this.sessionId}`);
      this.conversationHistory = [];
      this.sessionId = `voice_cli_${Date.now()}`;
      console.log('✅ Conversation cleared');
    } catch (error) {
      console.error('❌ Error clearing conversation:', error.message);
    }
  }

  // Main conversation loop
  async startConversation() {
    this.displayWelcome();
    
    // Test server connection
    try {
      await axios.get(`${BASE_URL}/health`);
      console.log('✅ Connected to Qala-Lwazi server\n');
    } catch (error) {
      console.error('❌ Cannot connect to server. Please start the server first:');
      console.error('   npm start\n');
      process.exit(1);
    }
    
    while (true) {
      const input = await this.askQuestion('\n🎤 Enter your message (or command): ');
      
      if (input.toLowerCase() === 'quit' || input.toLowerCase() === 'exit') {
        console.log('\n👋 Goodbye! Thank you for testing Qala-Lwazi voice capabilities.');
        break;
      }
      
      if (input.toLowerCase() === 'history') {
        this.displayHistory();
        continue;
      }
      
      if (input.toLowerCase() === 'clear') {
        await this.clearConversation();
        continue;
      }
      
      if (input.toLowerCase().startsWith('tts ')) {
        const text = input.substring(4);
        await this.testTextToSpeech(text);
        continue;
      }
      
      if (input.toLowerCase() === 'voice') {
        const message = await this.askQuestion('Enter the message to simulate as voice input: ');
        await this.simulateVoiceConversation(message);
        continue;
      }
      
      // Regular medical conversation
      if (input.trim()) {
        await this.simulateVoiceConversation(input);
      }
    }
    
    this.rl.close();
  }

  // Helper method to ask questions
  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer);
      });
    });
  }
}

// Run the CLI if called directly
if (require.main === module) {
  const cli = new VoiceConversationCLI();
  cli.startConversation().catch(error => {
    console.error('❌ CLI Error:', error);
    process.exit(1);
  });
}

module.exports = VoiceConversationCLI;
