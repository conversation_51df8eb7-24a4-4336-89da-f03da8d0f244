version: '3.8'

services:
  qala-lwazi-backend:
    build:
      context: .
      dockerfile: Dockerfile
    image: alphafrederic94/qala-lwazi-backend:latest
    container_name: qala-lwazi-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - PINECONE_API_KEY=${PINECONE_API_KEY}
      - PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT}
      - PINECONE_INDEX_NAME=${PINECONE_INDEX_NAME}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    volumes:
      - uploads:/app/uploads
      - ./fine-tuning-data:/app/fine-tuning-data
    networks:
      - qala-lwazi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  qala-lwazi-network:
    driver: bridge

volumes:
  uploads:
    driver: local
