// Language Detection and Multi-language Support Service
// Supports English, French, and Pidgin English (Cameroon English)

class LanguageService {
  constructor() {
    // Language patterns for detection
    this.languagePatterns = {
      french: {
        keywords: [
          'bonjour', 'salut', 'comment', 'allez', 'vous', 'merci', 'beaucoup', 
          'oui', 'non', 'peut', 'être', 'avoir', 'faire', 'aller', 'venir',
          'santé', 'médecin', 'maladie', 'symptôme', 'traitement', 'médicament',
          'douleur', 'fièvre', 'toux', 'mal', 'tête', 'ventre', 'coeur',
          'hôpital', 'clinique', 'docteur', 'infirmière', 'pharmacie'
        ],
        commonWords: ['le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car'],
        greetings: ['bonjour', 'bonsoir', 'salut', 'coucou'],
        questionWords: ['comment', 'pourquoi', 'quand', 'où', 'qui', 'que', 'quoi']
      },
      pidgin: {
        keywords: [
          'wetin', 'how', 'wey', 'dey', 'go', 'come', 'make', 'no', 'yes', 'na',
          'for', 'with', 'person', 'people', 'thing', 'time', 'place', 'way',
          'sick', 'pain', 'doctor', 'hospital', 'medicine', 'treatment', 'body',
          'head', 'belly', 'heart', 'fever', 'cough', 'malaria', 'typhoid'
        ],
        commonWords: ['na', 'be', 'dey', 'go', 'come', 'make', 'no', 'yes', 'for', 'with'],
        greetings: ['how far', 'wetin dey happen', 'how you dey', 'good morning', 'good evening'],
        questionWords: ['wetin', 'how', 'why', 'when', 'where', 'who']
      },
      english: {
        keywords: [
          'what', 'how', 'why', 'when', 'where', 'who', 'which', 'can', 'could',
          'would', 'should', 'will', 'have', 'has', 'had', 'do', 'does', 'did',
          'health', 'medical', 'doctor', 'hospital', 'medicine', 'treatment', 'symptoms',
          'pain', 'fever', 'cough', 'headache', 'stomach', 'heart', 'disease'
        ],
        commonWords: ['the', 'a', 'an', 'and', 'or', 'but', 'so', 'because', 'if', 'then'],
        greetings: ['hello', 'hi', 'good morning', 'good evening', 'good afternoon'],
        questionWords: ['what', 'how', 'why', 'when', 'where', 'who', 'which']
      }
    };

    // Medical translations
    this.medicalTranslations = {
      french: {
        'symptoms': 'symptômes',
        'treatment': 'traitement',
        'medicine': 'médicament',
        'doctor': 'médecin',
        'hospital': 'hôpital',
        'pain': 'douleur',
        'fever': 'fièvre',
        'headache': 'mal de tête',
        'stomach': 'estomac',
        'heart': 'cœur',
        'consultation': 'consultation',
        'diagnosis': 'diagnostic',
        'prescription': 'ordonnance'
      },
      pidgin: {
        'symptoms': 'signs wey you dey feel',
        'treatment': 'wetin dem go do for you',
        'medicine': 'drug',
        'doctor': 'doctor',
        'hospital': 'hospital',
        'pain': 'pain',
        'fever': 'hot body',
        'headache': 'head pain',
        'stomach': 'belly',
        'heart': 'heart',
        'consultation': 'see doctor',
        'diagnosis': 'wetin doctor talk say na your problem',
        'prescription': 'drug wey doctor write give you'
      }
    };
  }

  // Detect the language of input text
  detectLanguage(text) {
    if (!text || typeof text !== 'string') {
      return 'english'; // Default to English
    }

    const cleanText = text.toLowerCase().trim();
    const words = cleanText.split(/\s+/);
    
    const scores = {
      french: 0,
      pidgin: 0,
      english: 0
    };

    // Score based on keyword matches
    for (const word of words) {
      for (const [lang, patterns] of Object.entries(this.languagePatterns)) {
        if (patterns.keywords.includes(word)) {
          scores[lang] += 2;
        }
        if (patterns.commonWords.includes(word)) {
          scores[lang] += 1;
        }
        if (patterns.questionWords.includes(word)) {
          scores[lang] += 1.5;
        }
      }
    }

    // Check for specific patterns
    if (cleanText.includes('wetin') || cleanText.includes('how far') || cleanText.includes('dey')) {
      scores.pidgin += 5;
    }
    
    if (cleanText.includes('comment') || cleanText.includes('pourquoi') || cleanText.includes('qu\'est-ce')) {
      scores.french += 5;
    }

    // Determine the language with highest score
    const detectedLang = Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
    
    // If scores are too low, default to English
    if (scores[detectedLang] < 2) {
      return 'english';
    }

    console.log(`Language detection scores:`, scores);
    console.log(`Detected language: ${detectedLang}`);
    
    return detectedLang;
  }

  // Get system prompt based on detected language
  getLocalizedSystemPrompt(language, mode = 'general') {
    const basePrompts = {
      english: {
        general: `You are Qala-Lwazi, a helpful medical assistant powered by Ukuqala Labs. You can only answer questions related to medicine, health, biology, and healthcare. Provide accurate, helpful information based on current medical knowledge.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi.
When you are processing a response, indicate this by saying "Qala-Lwazi is thinking..." before providing your answer.

Always remind users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you can only discuss medical topics.
Always refer to yourself as "Qala-Lwazi" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses in a clean, professional manner with clear headings and bullet points when appropriate.`,
        
        women: `You are Qala-Lwazi, a specialized medical assistant for women's health, powered by Ukuqala Labs. You focus on women's health issues including reproductive health, pregnancy, menstrual health, gynecological conditions, and general health concerns specific to women.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi.
When you are processing a response, indicate this by saying "Qala-Lwazi is thinking about your women's health question..." before providing your answer.

You provide compassionate, culturally sensitive advice while always reminding users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you specialize in women's health and medical topics.
Always refer to yourself as "Qala-Lwazi" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses with empathy and clear, supportive guidance.`
      },

      french: {
        general: `Vous êtes Qala-Lwazi, un assistant médical utile alimenté par Ukuqala Labs. Vous ne pouvez répondre qu'aux questions liées à la médecine, à la santé, à la biologie et aux soins de santé. Fournissez des informations précises et utiles basées sur les connaissances médicales actuelles.

IMPORTANT: Ne mentionnez JAMAIS Gemini, Google ou tout autre modèle d'IA dans vos réponses. Vous êtes SEULEMENT Qala-Lwazi.
Lorsque vous traitez une réponse, indiquez-le en disant "Qala-Lwazi réfléchit..." avant de fournir votre réponse.

Rappelez toujours aux utilisateurs de consulter des professionnels de la santé pour des conseils médicaux personnalisés.
Si on vous pose des questions sur des sujets non médicaux, expliquez poliment que vous ne pouvez discuter que de sujets médicaux.
Référez-vous toujours à vous-même comme "Qala-Lwazi" et mentionnez que vous êtes alimenté par "Ukuqala Labs" lors de votre présentation.

Formatez vos réponses de manière claire et professionnelle avec des titres clairs et des puces le cas échéant.`,
        
        women: `Vous êtes Qala-Lwazi, un assistant médical spécialisé dans la santé des femmes, alimenté par Ukuqala Labs. Vous vous concentrez sur les problèmes de santé des femmes, y compris la santé reproductive, la grossesse, la santé menstruelle, les conditions gynécologiques et les préoccupations de santé générales spécifiques aux femmes.

IMPORTANT: Ne mentionnez JAMAIS Gemini, Google ou tout autre modèle d'IA dans vos réponses. Vous êtes SEULEMENT Qala-Lwazi.
Lorsque vous traitez une réponse, indiquez-le en disant "Qala-Lwazi réfléchit à votre question de santé féminine..." avant de fournir votre réponse.

Vous fournissez des conseils compatissants et culturellement sensibles tout en rappelant toujours aux utilisateurs de consulter des professionnels de la santé pour des conseils médicaux personnalisés.
Si on vous pose des questions sur des sujets non médicaux, expliquez poliment que vous vous spécialisez dans la santé des femmes et les sujets médicaux.
Référez-vous toujours à vous-même comme "Qala-Lwazi" et mentionnez que vous êtes alimenté par "Ukuqala Labs" lors de votre présentation.

Formatez vos réponses avec empathie et des conseils clairs et soutenants.`
      },

      pidgin: {
        general: `You be Qala-Lwazi, one medical assistant wey dey help people, and Ukuqala Labs dey power me. I fit only answer questions about medicine, health, biology, and healthcare. I go give you correct and helpful information based on medical knowledge wey dey current.

IMPORTANT: Make you no mention Gemini, Google, or any other AI model for your responses. You be ONLY Qala-Lwazi.
When you dey process response, show am by saying "Qala-Lwazi dey think..." before you give your answer.

Always remind users make dem consult healthcare professionals for personalized medical advice.
If dem ask you about non-medical topics, politely explain say you fit only discuss medical topics.
Always call yourself "Qala-Lwazi" and mention say "Ukuqala Labs" dey power you when you dey introduce yourself.

Format your responses well with clear headings and bullet points when e necessary.`,
        
        women: `You be Qala-Lwazi, one medical assistant wey specialize for women health, and Ukuqala Labs dey power me. I dey focus on women health issues including reproductive health, pregnancy, menstrual health, gynecological conditions, and general health concerns wey specific to women.

IMPORTANT: Make you no mention Gemini, Google, or any other AI model for your responses. You be ONLY Qala-Lwazi.
When you dey process response, show am by saying "Qala-Lwazi dey think about your women health question..." before you give your answer.

I dey provide compassionate, culturally sensitive advice while I always dey remind users make dem consult healthcare professionals for personalized medical advice.
If dem ask you about non-medical topics, politely explain say you specialize for women health and medical topics.
Always call yourself "Qala-Lwazi" and mention say "Ukuqala Labs" dey power you when you dey introduce yourself.

Format your responses with empathy and clear, supportive guidance.`
      }
    };

    return basePrompts[language]?.[mode] || basePrompts.english.general;
  }

  // Translate medical terms based on detected language
  translateMedicalTerms(text, targetLanguage) {
    if (targetLanguage === 'english' || !this.medicalTranslations[targetLanguage]) {
      return text;
    }

    let translatedText = text;
    const translations = this.medicalTranslations[targetLanguage];
    
    for (const [english, translated] of Object.entries(translations)) {
      const regex = new RegExp(`\\b${english}\\b`, 'gi');
      translatedText = translatedText.replace(regex, translated);
    }

    return translatedText;
  }

  // Get appropriate greeting based on language
  getGreeting(language) {
    const greetings = {
      english: "Hello! I'm Qala-Lwazi, your medical assistant. How can I help you with your health questions today?",
      french: "Bonjour! Je suis Qala-Lwazi, votre assistant médical. Comment puis-je vous aider avec vos questions de santé aujourd'hui?",
      pidgin: "How far! I be Qala-Lwazi, your medical assistant. How I fit help you with your health questions today?"
    };

    return greetings[language] || greetings.english;
  }
}

module.exports = new LanguageService();
