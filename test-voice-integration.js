// Test script for Voice Integration with Gemini
// Tests LangChain enhancements and voice capabilities

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';

class VoiceIntegrationTester {
  constructor() {
    this.testResults = [];
    this.sessionId = `test_session_${Date.now()}`;
  }

  // Test 1: Health check with new features
  async testHealthCheck() {
    console.log('🏥 Testing health check endpoint...');
    
    try {
      const response = await axios.get(`${BASE_URL}/health`);
      const health = response.data;
      
      const requiredFeatures = [
        'langchainIntegration',
        'voiceAssistant', 
        'speechToText',
        'textToSpeech',
        'geminiNativeAudio'
      ];
      
      const missingFeatures = requiredFeatures.filter(feature => !health.features[feature]);
      
      if (missingFeatures.length === 0) {
        console.log('✅ Health check passed - all voice features available');
        this.testResults.push({ test: 'Health Check', status: 'PASS' });
      } else {
        console.log(`❌ Health check failed - missing features: ${missingFeatures.join(', ')}`);
        this.testResults.push({ test: 'Health Check', status: 'FAIL', details: missingFeatures });
      }
      
      console.log('Voice Capabilities:', health.voiceCapabilities);
      
    } catch (error) {
      console.error('❌ Health check failed:', error.message);
      this.testResults.push({ test: 'Health Check', status: 'ERROR', error: error.message });
    }
  }

  // Test 2: Text-to-Speech functionality
  async testTextToSpeech() {
    console.log('🔊 Testing text-to-speech endpoint...');
    
    const testTexts = [
      { text: 'Hello, I am Qala-Lwazi, your medical assistant. How can I help you today?', language: 'english' },
      { text: 'Bonjour, je suis Qala-Lwazi, votre assistant médical. Comment puis-je vous aider?', language: 'french' },
      { text: 'Hello, I be Qala-Lwazi, your medical helper. Wetin I fit do for you today?', language: 'pidgin' }
    ];

    for (const testCase of testTexts) {
      try {
        console.log(`  Testing ${testCase.language}: "${testCase.text.substring(0, 30)}..."`);
        
        const response = await axios.post(`${BASE_URL}/api/text-to-speech`, {
          text: testCase.text,
          language: testCase.language
        });

        if (response.data.success && response.data.audioData) {
          console.log(`  ✅ TTS ${testCase.language} successful - ${response.data.duration}s audio generated`);
          this.testResults.push({ 
            test: `TTS ${testCase.language}`, 
            status: 'PASS',
            duration: response.data.duration 
          });
        } else {
          console.log(`  ❌ TTS ${testCase.language} failed - no audio data`);
          this.testResults.push({ test: `TTS ${testCase.language}`, status: 'FAIL' });
        }

      } catch (error) {
        console.error(`  ❌ TTS ${testCase.language} error:`, error.response?.data?.error || error.message);
        this.testResults.push({ 
          test: `TTS ${testCase.language}`, 
          status: 'ERROR', 
          error: error.response?.data?.error || error.message 
        });
      }
    }
  }

  // Test 3: Enhanced conversation with LangChain
  async testEnhancedConversation() {
    console.log('🧠 Testing enhanced conversation with LangChain...');
    
    const conversationFlow = [
      'I have been having chest pain for the past two days',
      'The pain gets worse when I exercise',
      'Should I be worried about my heart?'
    ];

    for (let i = 0; i < conversationFlow.length; i++) {
      try {
        console.log(`  Message ${i + 1}: "${conversationFlow[i]}"`);
        
        const response = await axios.post(`${BASE_URL}/api/medical-chat`, {
          message: conversationFlow[i],
          sessionId: this.sessionId,
          userPreferences: {
            useRAG: true,
            detailLevel: 'medium'
          }
        });

        if (response.data.response) {
          console.log(`  ✅ Response ${i + 1} received (${response.data.responseLength} chars)`);
          console.log(`     RAG used: ${response.data.usingRAG}`);
          console.log(`     Language: ${response.data.detectedLanguage}`);
          
          // Check if response mentions conversation context
          const hasContext = response.data.response.toLowerCase().includes('mentioned') || 
                           response.data.response.toLowerCase().includes('earlier') ||
                           response.data.response.toLowerCase().includes('previous');
          
          this.testResults.push({ 
            test: `Enhanced Conversation ${i + 1}`, 
            status: 'PASS',
            hasContext: hasContext,
            ragUsed: response.data.usingRAG
          });
        } else {
          console.log(`  ❌ No response received for message ${i + 1}`);
          this.testResults.push({ test: `Enhanced Conversation ${i + 1}`, status: 'FAIL' });
        }

      } catch (error) {
        console.error(`  ❌ Conversation error ${i + 1}:`, error.response?.data?.error || error.message);
        this.testResults.push({ 
          test: `Enhanced Conversation ${i + 1}`, 
          status: 'ERROR', 
          error: error.response?.data?.error || error.message 
        });
      }
    }
  }

  // Test 4: Language detection and multilingual responses
  async testMultilingualSupport() {
    console.log('🌍 Testing multilingual support...');
    
    const multilingualTests = [
      { message: 'I have a headache', expectedLang: 'english' },
      { message: 'J\'ai mal à la tête', expectedLang: 'french' },
      { message: 'My head dey pain me', expectedLang: 'pidgin' }
    ];

    for (const test of multilingualTests) {
      try {
        console.log(`  Testing: "${test.message}" (expected: ${test.expectedLang})`);
        
        const response = await axios.post(`${BASE_URL}/api/medical-chat`, {
          message: test.message,
          sessionId: `lang_test_${Date.now()}`
        });

        const detectedLang = response.data.detectedLanguage;
        const sessionLang = response.data.sessionLanguage;
        
        if (detectedLang === test.expectedLang || sessionLang === test.expectedLang) {
          console.log(`  ✅ Language detection correct: ${detectedLang} -> ${sessionLang}`);
          this.testResults.push({ 
            test: `Language Detection ${test.expectedLang}`, 
            status: 'PASS',
            detected: detectedLang,
            session: sessionLang
          });
        } else {
          console.log(`  ❌ Language detection incorrect: expected ${test.expectedLang}, got ${detectedLang}`);
          this.testResults.push({ 
            test: `Language Detection ${test.expectedLang}`, 
            status: 'FAIL',
            expected: test.expectedLang,
            detected: detectedLang
          });
        }

      } catch (error) {
        console.error(`  ❌ Multilingual test error:`, error.response?.data?.error || error.message);
        this.testResults.push({ 
          test: `Language Detection ${test.expectedLang}`, 
          status: 'ERROR', 
          error: error.response?.data?.error || error.message 
        });
      }
    }
  }

  // Test 5: Women's health mode activation
  async testWomensHealthMode() {
    console.log('👩‍⚕️ Testing women\'s health mode activation...');
    
    const womensHealthQueries = [
      'I have irregular periods',
      'I think I might be pregnant',
      'I have questions about menopause'
    ];

    for (const query of womensHealthQueries) {
      try {
        console.log(`  Testing: "${query}"`);
        
        const response = await axios.post(`${BASE_URL}/api/medical-chat`, {
          message: query,
          sessionId: `womens_test_${Date.now()}`
        });

        const womensMode = response.data.womensHealthMode;
        const medicalMode = response.data.medicalMode;
        
        if (womensMode && medicalMode === 'women') {
          console.log(`  ✅ Women's health mode activated correctly`);
          this.testResults.push({ 
            test: `Women's Health Mode`, 
            status: 'PASS',
            query: query.substring(0, 30)
          });
        } else {
          console.log(`  ❌ Women's health mode not activated (mode: ${medicalMode})`);
          this.testResults.push({ 
            test: `Women's Health Mode`, 
            status: 'FAIL',
            query: query.substring(0, 30),
            mode: medicalMode
          });
        }

      } catch (error) {
        console.error(`  ❌ Women's health test error:`, error.response?.data?.error || error.message);
        this.testResults.push({ 
          test: `Women's Health Mode`, 
          status: 'ERROR', 
          error: error.response?.data?.error || error.message 
        });
      }
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Voice Integration Tests...\n');
    
    await this.testHealthCheck();
    console.log('');
    
    await this.testTextToSpeech();
    console.log('');
    
    await this.testEnhancedConversation();
    console.log('');
    
    await this.testMultilingualSupport();
    console.log('');
    
    await this.testWomensHealthMode();
    console.log('');
    
    this.printSummary();
  }

  // Print test summary
  printSummary() {
    console.log('📊 TEST SUMMARY');
    console.log('================');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`🔥 Errors: ${errors}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    console.log('\nDetailed Results:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '🔥';
      console.log(`${status} ${result.test}: ${result.status}`);
      if (result.error) console.log(`   Error: ${result.error}`);
      if (result.details) console.log(`   Details: ${JSON.stringify(result.details)}`);
    });
    
    // Save results to file
    const reportPath = path.join(__dirname, `voice_test_report_${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: { total, passed, failed, errors },
      results: this.testResults
    }, null, 2));
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new VoiceIntegrationTester();
  
  // Check if server is running
  axios.get(`${BASE_URL}/health`)
    .then(() => {
      console.log('✅ Server is running, starting tests...\n');
      return tester.runAllTests();
    })
    .catch(error => {
      console.error('❌ Server is not running. Please start the server first:');
      console.error('   npm start');
      console.error('\nError:', error.message);
      process.exit(1);
    });
}

module.exports = VoiceIntegrationTester;
