{"conversationFlowTests": [{"name": "Diabetes Follow-up Conversation", "messages": ["I was recently diagnosed with diabetes", "What should I eat?", "What about exercise?", "How often should I check my blood sugar?", "What are the warning signs I should watch for?"], "expectedBehavior": "Should maintain context about diabetes diagnosis throughout conversation"}, {"name": "Pregnancy Consultation Flow", "messages": ["I think I might be pregnant", "What are the early signs?", "When should I see a doctor?", "What should I avoid during pregnancy?", "What vitamins should I take?"], "expectedBehavior": "Should activate women's health mode and maintain pregnancy context"}, {"name": "Chest Pain Emergency Assessment", "messages": ["I'm having chest pain", "It started an hour ago", "It's getting worse", "Should I go to the hospital?", "What should I do while waiting for help?"], "expectedBehavior": "Should recognize urgency and provide appropriate emergency guidance"}], "multiLanguageTests": [{"language": "french", "scenarios": [{"query": "<PERSON><PERSON><PERSON>, j'ai de la fièvre depuis deux jours", "expectedResponse": "Should respond in French about fever management"}, {"query": "Quels sont les symptômes du diabète?", "expectedResponse": "Should provide diabetes symptoms information in French"}, {"query": "Je suis enceinte, que dois-je éviter?", "expectedResponse": "Should activate women's health mode and respond in French about pregnancy"}]}, {"language": "pidgin", "scenarios": [{"query": "My head dey pain me well well", "expectedResponse": "Should respond in Pidgin English about headache treatment"}, {"query": "Wetin I fit do for cough wey no dey stop?", "expectedResponse": "Should provide cough treatment advice in Pidgin English"}, {"query": "I dey feel say I get malaria", "expectedResponse": "Should respond about malaria symptoms and treatment in Pidgin"}]}], "womensHealthTests": [{"category": "reproductive", "queries": ["I have irregular periods", "I'm trying to get pregnant", "I have painful menstruation", "What is PCOS?", "I think I have endometriosis"]}, {"category": "maternal", "queries": ["I'm 8 weeks pregnant and having morning sickness", "What should I eat during pregnancy?", "I'm breastfeeding and have questions", "I think I have postpartum depression", "When can I exercise after giving birth?"]}, {"category": "gynecological", "queries": ["I have unusual vaginal discharge", "When should I get a pap smear?", "I found a lump in my breast", "I have recurring UTIs", "What are the signs of ovarian cancer?"]}, {"category": "hormonal", "queries": ["I think I'm going through menopause", "I have severe PMS symptoms", "My hormones seem imbalanced", "What is hormone replacement therapy?", "I have thyroid problems"]}], "ragAndCitationTests": [{"query": "What are the latest treatments for hypertension?", "expectedCitations": true, "expectedSources": ["Medical Handbook", "Clinical Guidelines"]}, {"query": "How is Type 2 diabetes diagnosed?", "expectedCitations": true, "expectedSources": ["Diagnostic Criteria", "Laboratory Guidelines"]}, {"query": "What are the risk factors for heart disease?", "expectedCitations": true, "expectedSources": ["Cardiovascular Guidelines", "Risk Assessment"]}], "fileUploadTests": [{"fileType": "medical_report", "content": "PATIENT: <PERSON>\nSYMPTOMS: Chest pain, shortness of breath\nDURATION: 2 days\nVITALS: BP 140/90, HR 88\nASSESSMENT: Possible cardiac event", "expectedAnalysis": "Should analyze symptoms and provide relevant medical guidance"}, {"fileType": "lab_results", "content": "GLUCOSE: 180 mg/dL (HIGH)\nHbA1c: 8.5% (HIGH)\nCHOLESTEROL: 240 mg/dL (HIGH)\nBP: 150/95 mmHg (HIGH)", "expectedAnalysis": "Should identify diabetes and cardiovascular risk factors"}], "edgeCaseTests": [{"name": "Mixed Language Input", "query": "Hello, je have mal à la tête and my head dey pain me", "expectedBehavior": "Should detect primary language and respond appropriately"}, {"name": "Non-Medical Query", "query": "What's the weather like today?", "expectedBehavior": "Should politely redirect to medical topics"}, {"name": "Emergency Keywords", "query": "I think I'm having a heart attack", "expectedBehavior": "Should recognize emergency and provide immediate guidance"}, {"name": "Very Long Conversation", "description": "Test with 50+ messages to check memory management", "expectedBehavior": "Should maintain relevant context without performance degradation"}], "performanceTests": [{"name": "Response Time", "description": "Measure average response time for different query types", "acceptableThreshold": "< 5 seconds"}, {"name": "Concurrent Users", "description": "Test multiple simultaneous conversations", "testLoad": "10 concurrent sessions"}, {"name": "Memory Usage", "description": "Monitor memory usage during extended conversations", "acceptableThreshold": "< 500MB per session"}]}