#!/bin/bash

echo "🧪 Testing PDF Upload and Processing..."

# Test 1: Upload a PDF file
echo "📄 Step 1: Testing PDF file upload..."
curl -X POST http://localhost:3001/api/upload-files \
  -F "files=@Sample-filled-in-MR.pdf" \
  --connect-timeout 30 --max-time 60 \
  -o upload_response.json

echo ""
echo "📋 Upload Response:"
cat upload_response.json | jq '.'

echo ""
echo "🤖 Step 2: Testing medical analysis with PDF..."

# Create a test request with the uploaded PDF data
cat > test_pdf_chat.json << 'EOF'
{
  "message": "Please analyze this medical record and provide your assessment.",
  "medicalMode": "general",
  "userPreferences": {
    "useRAG": false,
    "detailLevel": "detailed",
    "creativity": "balanced",
    "responseLength": "medium"
  },
  "files": [
    {
      "id": 1748239621008.7844,
      "name": "Sample-filled-in-MR.pdf",
      "size": 50000,
      "type": "application/pdf",
      "geminiUri": "local://Sample-filled-in-MR.pdf",
      "geminiName": "Sample-filled-in-MR.pdf",
      "processed": true,
      "contentLength": 2000
    }
  ]
}
EOF

# Test medical chat with PDF
curl -X POST http://localhost:3001/api/medical-chat \
  -H "Content-Type: application/json" \
  -d @test_pdf_chat.json \
  --connect-timeout 60 --max-time 120 \
  -o chat_response.json

echo ""
echo "🏥 Medical Analysis Response:"
cat chat_response.json | jq '.response' | head -20

echo ""
echo "✅ PDF Testing Complete!"
