# CareAI Medical Chatbot - Enhanced Backend with Voice AI

A comprehensive medical chatbot backend powered by Google Gemini AI with advanced features including **voice assistance**, multi-language support, enhanced conversation memory, women's health specialization, and improved RAG (Retrieval-Augmented Generation) with proper citations.

## 🚀 Features

### Core Features
- **Medical AI Assistant**: Powered by Google Gemini AI for accurate medical information
- **RAG System**: Enhanced retrieval from medical handbook with proper citations
- **File Upload**: Support for medical reports, lab results, and documents
- **Conversation History**: Persistent storage with Supabase

### New Enhanced Features
- **🎤 Voice Assistant**: Full speech-to-text and text-to-speech capabilities with Gemini Native Audio
- **🧠 LangChain Integration**: Advanced conversation chains and memory management
- **🌍 Multi-Language Support**: English, French, and Pidgin English (Cameroon) with voice support
- **🔊 Medical Voice Optimization**: Specialized pronunciation for medical terminology
- **👩‍⚕️ Women's Health Mode**: Specialized mode for women's health questions
- **📚 Enhanced Citations**: Better reference system with source attribution
- **🔄 Intelligent Context Management**: Smart conversation flow with topic tracking

## 🛠️ Technology Stack

- **Backend**: Node.js with Express
- **AI Model**: Google Gemini 2.5 Flash with Native Audio
- **Voice AI**: Gemini TTS and STT for multilingual voice interaction
- **Conversation AI**: LangChain for advanced conversation management
- **Vector Database**: Pinecone for medical handbook retrieval
- **Database**: Supabase for conversation storage
- **Language Processing**: Custom language detection and translation
- **File Processing**: PDF parsing, text extraction, audio processing

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Google Gemini API key
- Supabase account and credentials
- Pinecone account and API key

## ⚙️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd careai-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   Create a `.env` file with the following:
   ```env
   # Server Configuration
   NODE_ENV=production
   PORT=3001

   # Google Gemini API
   GEMINI_API_KEY=your_gemini_api_key

   # Supabase Configuration
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key

   # Pinecone Configuration
   PINECONE_API_KEY=your_pinecone_api_key
   PINECONE_INDEX_NAME=medical-handbook

   # LangChain (Optional)
   LANGCHAIN_API_KEY=your_langchain_api_key

   # Optional Configuration
   CORS_ORIGIN=http://localhost:3000
   MAX_FILE_SIZE=********
   UPLOAD_LIMIT=5
   ```

4. **Set up database**
   Run the Supabase SQL scripts in the `supabase/` directory:
   ```bash
   # Execute chat_messages_updated.sql in your Supabase SQL editor
   ```

5. **Start the server**
   ```bash
   npm start
   # or for development
   npm run dev
   ```

## 🔧 API Endpoints

### Health Check
```http
GET /health
```
Returns server status and available features.

### Language Detection
```http
POST /api/detect-language
Content-Type: application/json

{
  "text": "Hello, how are you?"
}
```

### Medical Chat
```http
POST /api/medical-chat
Content-Type: application/json

{
  "message": "I have a headache",
  "sessionId": "optional_session_id",
  "medicalMode": "general|women",
  "userPreferences": {
    "useRAG": true,
    "detailLevel": "simple|medium|detailed",
    "creativity": "conservative|balanced|creative",
    "responseLength": "short|medium|long",
    "includeReferences": true
  },
  "files": []
}
```

### File Upload
```http
POST /api/upload-files
Content-Type: multipart/form-data

files: [medical_report.pdf, lab_results.txt]
```

### Conversation History
```http
GET /api/chat-history/:sessionId
DELETE /api/chat-history/:sessionId
```

### Voice Assistant Endpoints

#### Voice Chat (Complete Voice Interaction)
```http
POST /api/voice-chat
Content-Type: multipart/form-data

audio: [audio file]
sessionId: "optional_session_id"
language: "english|french|pidgin"
userPreferences: {
  "useRAG": true,
  "detailLevel": "simple|medium|detailed"
}
```

#### Text-to-Speech
```http
POST /api/text-to-speech
Content-Type: application/json

{
  "text": "Hello, I am your medical assistant",
  "language": "english|french|pidgin",
  "voice": "medical-assistant"
}
```

#### Speech-to-Text
```http
POST /api/speech-to-text
Content-Type: multipart/form-data

audio: [audio file]
language: "english|french|pidgin"
```

## 🌍 Multi-Language Support

The system automatically detects and responds in:

### English
```json
{
  "message": "I have a headache",
  "detectedLanguage": "english"
}
```

### French
```json
{
  "message": "J'ai mal à la tête",
  "detectedLanguage": "french"
}
```

### Pidgin English (Cameroon)
```json
{
  "message": "My head dey pain me",
  "detectedLanguage": "pidgin"
}
```

## 👩‍⚕️ Women's Health Mode

Automatically activated for women's health queries:

- **Reproductive Health**: Menstruation, fertility, contraception
- **Maternal Health**: Pregnancy, postpartum, breastfeeding
- **Gynecological**: Screenings, infections, conditions
- **Hormonal**: Menopause, PMS, hormone therapy
- **Mental Health**: Postpartum depression, body image

Example:
```json
{
  "message": "I have irregular periods",
  "medicalMode": "general",
  "response": {
    "womensHealthMode": true,
    "effectiveMedicalMode": "women"
  }
}
```

## 🧠 Enhanced Conversation Memory

Features:
- **Context Tracking**: Maintains conversation topics and themes
- **Follow-up Detection**: Recognizes related questions
- **User Profiling**: Builds understanding of user preferences
- **Smart Summarization**: Generates conversation summaries
- **Relevance Scoring**: Prioritizes important context

## 📚 Enhanced RAG and Citations

Improved retrieval with proper citations:

```json
{
  "response": "Diabetes symptoms include [1] frequent urination, [2] excessive thirst...",
  "usingRAG": true,
  "citations": [
    "[1] Medical Handbook - Endocrine Disorders (Relevance: 95.2%)",
    "[2] Clinical Guidelines - Diabetes Diagnosis (Relevance: 89.7%)"
  ]
}
```

## 🧪 Testing

### Quick Tests
```bash
# Run basic tests
./run-tests.sh

# Start server and run tests
./run-tests.sh --start-server

# Run comprehensive CLI tests
./run-tests.sh --cli-tests

# Interactive testing mode
./run-tests.sh --interactive
```

### Comprehensive Testing
```bash
# Run all tests with detailed reporting
node test-cli.js

# Interactive mode for manual testing
node test-cli.js --interactive
```

### Test Coverage
- ✅ Server health and API endpoints
- ✅ Language detection (English, French, Pidgin)
- ✅ Conversation flow and memory
- ✅ Women's health mode activation
- ✅ Multi-language responses
- ✅ RAG system and citations
- ✅ File upload functionality
- ✅ Edge cases and error handling

## 📁 Project Structure

```
careai-backend/
├── server.js                 # Main server file
├── languageService.js        # Multi-language support
├── conversationMemoryService.js # Enhanced conversation memory
├── womensHealthService.js    # Women's health specialization
├── pineconeService.js        # Enhanced RAG with citations
├── supabaseClient.js         # Database operations
├── test-cli.js              # Comprehensive testing tool
├── run-tests.sh             # Test runner script
├── test-scenarios.json      # Test scenarios and cases
├── supabase/                # Database schemas
│   ├── chat_messages_updated.sql
│   └── check_table.sql
├── uploads/                 # File upload directory
└── fine-tuning-data/       # Medical embeddings data
```

## 🔒 Security Features

- **Input Validation**: Comprehensive request validation
- **File Type Restrictions**: Limited to safe file types
- **Rate Limiting**: Built-in request rate limiting
- **CORS Configuration**: Configurable cross-origin settings
- **Environment Variables**: Secure credential management

## 🚀 Deployment

### Local Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Docker (Optional)
```bash
docker build -t careai-backend .
docker run -p 3001:3001 careai-backend
```

## 📊 Monitoring and Logging

- **Request Logging**: Detailed API request logs
- **Error Tracking**: Comprehensive error handling
- **Performance Metrics**: Response time monitoring
- **Health Checks**: Regular system health monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the test suite for examples
- Review the API documentation
- Run interactive tests for debugging

## 🔄 Version History

### v2.0.0 (Current) - Enhanced CareAI
- ✅ Multi-language support (English, French, Pidgin English)
- ✅ Enhanced conversation memory and context flow
- ✅ Women's health specialization mode
- ✅ Improved RAG system with better citations
- ✅ Comprehensive testing suite with CLI tools
- ✅ Enhanced documentation and API reference
- ✅ Smart conversation context management
- ✅ Cultural sensitivity for African users
- ✅ Automatic mode switching based on query content

### v1.0.0 (Previous) - Basic CareAI
- Basic medical chatbot functionality
- Google Gemini AI integration
- Pinecone RAG system
- Supabase conversation storage
- File upload support

## 🎯 Key Improvements in v2.0.0

1. **Conversation Flow**: No longer treats each question as isolated - maintains context and follows conversation threads
2. **Language Intelligence**: Automatically detects and responds in user's preferred language
3. **Specialized Care**: Women's health mode provides culturally sensitive, specialized responses
4. **Better Citations**: Enhanced reference system with source attribution and relevance scoring
5. **Comprehensive Testing**: Full test suite with CLI tools for easy validation
6. **Memory Management**: Smart context management that remembers important details while managing memory efficiently
