# 🏥 Qala-<PERSON><PERSON>zi Medical Assistant Backend - Docker Deployment

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- API keys for Gemini, Pinecone, and Supabase

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit with your API keys
nano .env
```

### 2. Run with Docker Compose
```bash
# Start the service
docker-compose up -d

# Check logs
docker-compose logs -f

# Stop the service
docker-compose down
```

### 3. Run with Docker
```bash
# Pull from Docker Hub
docker pull alphafrederic94/qala-lwazi-backend:latest

# Run container
docker run -d \
  --name qala-lwazi-backend \
  -p 3001:3001 \
  --env-file .env \
  alphafrederic94/qala-lwazi-backend:latest
```

## 🔧 Configuration

### Required Environment Variables
```env
GEMINI_API_KEY=your_gemini_api_key
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=your_pinecone_index
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
```

### Optional Variables
```env
NODE_ENV=production
PORT=3001
```

## 🏗️ Building from Source

### Build Image
```bash
# Build locally
docker build -t qala-lwazi-backend .

# Build and tag for Docker Hub
docker build -t alphafrederic94/qala-lwazi-backend:latest .
```

### Deploy to Docker Hub
```bash
# Use deployment script
./deploy.sh

# Or manually
docker push alphafrederic94/qala-lwazi-backend:latest
```

## 🔍 Health Checks

The container includes health checks:
```bash
# Check container health
docker ps

# Manual health check
curl http://localhost:3001/health
```

## 📊 Monitoring

### View Logs
```bash
# Docker Compose
docker-compose logs -f qala-lwazi-backend

# Docker
docker logs -f qala-lwazi-backend
```

### Container Stats
```bash
docker stats qala-lwazi-backend
```

## 🛠️ Troubleshooting

### Common Issues

1. **Container won't start**
   - Check environment variables
   - Verify API keys are valid
   - Check port availability

2. **Health check failing**
   - Wait for startup (40s grace period)
   - Check application logs
   - Verify dependencies

3. **File upload issues**
   - Ensure uploads volume is mounted
   - Check file permissions

### Debug Mode
```bash
# Run with debug output
docker run -it --rm \
  --env-file .env \
  alphafrederic94/qala-lwazi-backend:latest \
  node server.js
```

## 🔐 Security

- Container runs as non-root user
- Minimal Alpine Linux base image
- Health checks enabled
- Environment variables for secrets

## 📈 Production Deployment

### Docker Swarm
```yaml
version: '3.8'
services:
  qala-lwazi-backend:
    image: alphafrederic94/qala-lwazi-backend:latest
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    secrets:
      - gemini_api_key
      - pinecone_api_key
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qala-lwazi-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: qala-lwazi-backend
  template:
    metadata:
      labels:
        app: qala-lwazi-backend
    spec:
      containers:
      - name: qala-lwazi-backend
        image: alphafrederic94/qala-lwazi-backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
```

## 🆘 Support

For issues and support:
- GitHub Issues: [Project Repository]
- Docker Hub: https://hub.docker.com/r/alphafrederic94/qala-lwazi-backend
- Documentation: [Project Docs]
