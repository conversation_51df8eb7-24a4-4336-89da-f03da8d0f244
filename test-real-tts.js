#!/usr/bin/env node

// Real TTS Test - Test actual Gemini TTS and confirm you hear it
// This will test the official Gemini TTS API and ask for confirmation

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const readline = require('readline');

const BASE_URL = 'http://localhost:3001';

class RealTTSTest {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // Play audio file using system audio player
  async playAudio(audioBuffer, format = 'wav') {
    return new Promise((resolve) => {
      try {
        // Save audio data to temporary file
        const tempFile = path.join(__dirname, `test_audio.${format}`);
        fs.writeFileSync(tempFile, audioBuffer);

        console.log('🔊 Playing audio... Listen carefully!');

        // Determine audio player based on OS
        let player;
        const platform = process.platform;

        if (platform === 'linux') {
          // Try different Linux audio players
          player = spawn('aplay', [tempFile]);
          player.on('error', () => {
            player = spawn('paplay', [tempFile]);
            player.on('error', () => {
              console.log('❌ No audio player found. Install: sudo apt-get install alsa-utils');
              resolve(false);
            });
          });
        } else if (platform === 'darwin') {
          // macOS
          player = spawn('afplay', [tempFile]);
        } else if (platform === 'win32') {
          // Windows
          player = spawn('powershell', ['-c', `(New-Object Media.SoundPlayer "${tempFile}").PlaySync()`]);
        } else {
          console.log('❌ Unsupported platform for audio playback');
          resolve(false);
          return;
        }

        player.on('close', (code) => {
          // Clean up temp file
          if (fs.existsSync(tempFile)) {
            fs.unlinkSync(tempFile);
          }
          
          if (code === 0) {
            console.log('✅ Audio playback completed');
            resolve(true);
          } else {
            console.log('❌ Audio playback failed');
            resolve(false);
          }
        });

        player.on('error', (error) => {
          console.log('❌ Audio playback error:', error.message);
          if (fs.existsSync(tempFile)) {
            fs.unlinkSync(tempFile);
          }
          resolve(false);
        });

      } catch (error) {
        console.error('❌ Error playing audio:', error.message);
        resolve(false);
      }
    });
  }

  // Test TTS with confirmation
  async testTTSWithConfirmation(text, language = 'english') {
    try {
      console.log(`\n🎤 TESTING GEMINI TTS`);
      console.log(`Text: "${text}"`);
      console.log(`Language: ${language}`);
      console.log('=' .repeat(50));

      // Call TTS API
      const response = await axios.post(`${BASE_URL}/api/text-to-speech`, {
        text: text,
        language: language
      });

      if (response.data.success) {
        console.log(`✅ TTS API call successful`);
        console.log(`   Method: ${response.data.method}`);
        console.log(`   Voice: ${response.data.voiceUsed || 'default'}`);
        console.log(`   Duration: ${response.data.duration}s`);
        console.log(`   Format: ${response.data.audioFormat}`);
        
        // Convert base64 to buffer
        const audioBuffer = Buffer.from(response.data.audioData, 'base64');
        
        // Play the audio
        const played = await this.playAudio(audioBuffer, response.data.audioFormat);
        
        if (played) {
          // Ask user if they heard it
          const heard = await this.askQuestion('\n❓ Did you ACTUALLY hear the AI speak? (yes/no): ');
          
          if (heard.toLowerCase().includes('yes') || heard.toLowerCase().includes('y')) {
            console.log('🎉 SUCCESS! You heard the AI speak!');
            return { success: true, heard: true, method: response.data.method };
          } else {
            console.log('❌ You didn\'t hear anything - there\'s still an issue');
            return { success: true, heard: false, method: response.data.method };
          }
        } else {
          console.log('❌ Could not play audio - audio system issue');
          return { success: true, heard: false, method: response.data.method, issue: 'playback' };
        }
      } else {
        console.log('❌ TTS API call failed');
        console.log('Error:', response.data.error);
        return { success: false, error: response.data.error };
      }
    } catch (error) {
      console.error('❌ TTS test error:', error.response?.data?.error || error.message);
      return { success: false, error: error.response?.data?.error || error.message };
    }
  }

  // Test multiple languages and voices
  async runComprehensiveTest() {
    console.log('🎤 COMPREHENSIVE GEMINI TTS TEST');
    console.log('='.repeat(60));
    console.log('This will test the official Gemini TTS API');
    console.log('You should ACTUALLY HEAR the AI speaking\n');

    // Check server
    try {
      await axios.get(`${BASE_URL}/health`);
      console.log('✅ Server is running\n');
    } catch (error) {
      console.error('❌ Server not running. Start with: npm start');
      process.exit(1);
    }

    const tests = [
      {
        text: 'Hello! I am Qala-Lwazi, your medical assistant. Can you hear me speaking clearly?',
        language: 'english',
        description: 'English Medical Introduction'
      },
      {
        text: 'I understand you are experiencing chest pain. This requires immediate medical attention.',
        language: 'english', 
        description: 'English Medical Response'
      },
      {
        text: 'Bonjour! Je suis Qala-Lwazi, votre assistant médical. Comment puis-je vous aider?',
        language: 'french',
        description: 'French Medical Introduction'
      },
      {
        text: 'Hello! I be Qala-Lwazi, your medical helper. Wetin I fit do for you today?',
        language: 'pidgin',
        description: 'Pidgin Medical Introduction'
      }
    ];

    const results = [];

    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      console.log(`\n${'='.repeat(60)}`);
      console.log(`TEST ${i + 1}/${tests.length}: ${test.description}`);
      console.log(`${'='.repeat(60)}`);
      
      const result = await this.testTTSWithConfirmation(test.text, test.language);
      results.push({
        ...test,
        ...result
      });

      // Wait a moment between tests
      if (i < tests.length - 1) {
        await this.askQuestion('\nPress Enter to continue to next test...');
      }
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('🏁 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    
    results.forEach((result, index) => {
      const status = result.heard ? '✅ HEARD' : result.success ? '⚠️ NO SOUND' : '❌ FAILED';
      console.log(`${status} Test ${index + 1}: ${result.description}`);
      if (result.method) console.log(`   Method: ${result.method}`);
      if (result.error) console.log(`   Error: ${result.error}`);
    });

    const heardCount = results.filter(r => r.heard).length;
    const successCount = results.filter(r => r.success).length;
    
    console.log(`\n📊 Results: ${heardCount}/${results.length} tests you actually heard`);
    console.log(`📊 API Success: ${successCount}/${results.length} API calls successful`);

    if (heardCount === results.length) {
      console.log('\n🎉 PERFECT! All voice tests successful - you heard everything!');
    } else if (heardCount > 0) {
      console.log('\n⚠️ PARTIAL SUCCESS - some voice tests worked');
    } else {
      console.log('\n❌ NO VOICE OUTPUT - you didn\'t hear anything');
      console.log('This means the TTS is generating audio but there\'s a playback issue');
    }

    this.rl.close();
  }

  // Helper method to ask questions
  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer);
      });
    });
  }
}

// Run if called directly
if (require.main === module) {
  const tester = new RealTTSTest();
  tester.runComprehensiveTest().catch(error => {
    console.error('❌ Test error:', error);
    process.exit(1);
  });
}

module.exports = RealTTSTest;
