# Qala-L<PERSON>zi Medical Assistant Backend Environment Variables

# Server Configuration
NODE_ENV=production
PORT=3001

# Google Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Pinecone Vector Database Configuration (for RAG)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=your_pinecone_index_name_here

# Supabase Database Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Optional: Additional Configuration
# CORS_ORIGIN=http://localhost:3000
# MAX_FILE_SIZE=10485760
# UPLOAD_LIMIT=5
