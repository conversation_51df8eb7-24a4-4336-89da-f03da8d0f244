{"timestamp": "2025-07-17T19:56:06.930Z", "summary": {"totalTests": 25, "passedTests": 18, "failedTests": 6, "partialTests": 1}, "results": [{"test": "Server Health", "status": "PASS", "details": {"status": "ok", "message": "Qala-Lwazi Medical Assistant API is running", "features": {"multiLanguage": true, "conversationMemory": true, "womensHealthMode": true, "enhancedRAG": true}}}, {"test": "Language Detection: Hello, how are you?", "status": "PASS", "expected": "english", "actual": "english"}, {"test": "Language Detection: <PERSON><PERSON><PERSON>, comment allez-vous?", "status": "PASS", "expected": "french", "actual": "french"}, {"test": "Language Detection: How far? Wetin dey happen?", "status": "PASS", "expected": "pidgin", "actual": "pidgin"}, {"test": "Language Detection: I have a headache", "status": "PASS", "expected": "english", "actual": "english"}, {"test": "Language Detection: J'ai mal à la tête", "status": "PASS", "expected": "french", "actual": "french"}, {"test": "Language Detection: My head dey pain me", "status": "PASS", "expected": "pidgin", "actual": "pidgin"}, {"test": "Conversation Flow Step 1", "status": "PASS", "message": "Hello, I have been having headaches", "responseLength": 1471, "topics": ["neurological", "digestive"]}, {"test": "Conversation Flow Step 2", "status": "PASS", "message": "They started last week", "responseLength": 1363, "topics": ["neurological", "digestive"]}, {"test": "Conversation Flow Step 3", "status": "PASS", "message": "What could be causing them?", "responseLength": 1847, "topics": ["neurological", "digestive"]}, {"test": "Conversation Flow Step 4", "status": "PASS", "message": "Are there any home remedies?", "responseLength": 1562, "topics": ["neurological", "digestive"]}, {"test": "Conversation History Retrieval", "status": "PASS", "messageCount": 8}, {"test": "Women's Health Detection: I have irregular periods", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I think I might be pregnant", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I have questions about menopause", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I need information about breast health", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I have concerns about reproductive health", "status": "PARTIAL", "womensHealthMode": false}, {"test": "Multi-Language: French headache query", "status": "PASS", "detectedLanguage": "french", "sessionLanguage": "english"}, {"test": "Multi-Language: Pidgin stomach pain query", "status": "PASS", "detectedLanguage": "pidgin", "sessionLanguage": "english"}, {"test": "Multi-Language: French fever treatment query", "status": "FAIL", "error": "socket hang up"}, {"test": "Multi-Language: Pidgin cough query", "status": "FAIL", "error": "connect ECONNREFUSED 127.0.0.1:3001"}, {"test": "Enhanced RAG: What are the symptoms of diabetes?", "status": "FAIL", "error": "connect ECONNREFUSED 127.0.0.1:3001"}, {"test": "Enhanced RAG: How is hypertension treated?", "status": "FAIL", "error": "connect ECONNREFUSED 127.0.0.1:3001"}, {"test": "Enhanced RAG: What causes heart disease?", "status": "FAIL", "error": "connect ECONNREFUSED 127.0.0.1:3001"}, {"test": "File Upload", "status": "FAIL", "error": "connect ECONNREFUSED 127.0.0.1:3001"}]}