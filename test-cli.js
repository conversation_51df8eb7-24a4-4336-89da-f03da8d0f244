#!/usr/bin/env node

// Comprehensive CLI Testing Tool for CareAI Medical Chatbot
// Tests all new features including multi-language support, conversation flow, women's health mode, and enhanced RAG

const axios = require('axios');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

class CareAITester {
  constructor() {
    this.baseURL = 'http://localhost:3001';
    this.sessionId = `test_session_${Date.now()}`;
    this.testResults = [];
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // Color codes for console output
  colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
  };

  log(message, color = 'reset') {
    console.log(`${this.colors[color]}${message}${this.colors.reset}`);
  }

  async runAllTests() {
    this.log('\n🚀 Starting CareAI Comprehensive Testing Suite', 'cyan');
    this.log('=' .repeat(60), 'cyan');

    try {
      // Test server health
      await this.testServerHealth();
      
      // Test language detection
      await this.testLanguageDetection();
      
      // Test conversation flow
      await this.testConversationFlow();
      
      // Test women's health mode
      await this.testWomensHealthMode();
      
      // Test multi-language support
      await this.testMultiLanguageSupport();
      
      // Test enhanced RAG and citations
      await this.testEnhancedRAG();
      
      // Test file upload functionality
      await this.testFileUpload();
      
      // Generate test report
      this.generateTestReport();
      
    } catch (error) {
      this.log(`❌ Testing failed: ${error.message}`, 'red');
    }
  }

  async testServerHealth() {
    this.log('\n📊 Testing Server Health...', 'yellow');
    
    try {
      const response = await axios.get(`${this.baseURL}/health`);
      
      if (response.status === 200 && response.data.status === 'ok') {
        this.log('✅ Server is healthy', 'green');
        this.log(`Features: ${JSON.stringify(response.data.features, null, 2)}`, 'blue');
        this.testResults.push({ test: 'Server Health', status: 'PASS', details: response.data });
      } else {
        throw new Error('Server health check failed');
      }
    } catch (error) {
      this.log(`❌ Server health check failed: ${error.message}`, 'red');
      this.testResults.push({ test: 'Server Health', status: 'FAIL', error: error.message });
    }
  }

  async testLanguageDetection() {
    this.log('\n🌍 Testing Language Detection...', 'yellow');
    
    const testCases = [
      { text: 'Hello, how are you?', expected: 'english' },
      { text: 'Bonjour, comment allez-vous?', expected: 'french' },
      { text: 'How far? Wetin dey happen?', expected: 'pidgin' },
      { text: 'I have a headache', expected: 'english' },
      { text: 'J\'ai mal à la tête', expected: 'french' },
      { text: 'My head dey pain me', expected: 'pidgin' }
    ];

    for (const testCase of testCases) {
      try {
        const response = await axios.post(`${this.baseURL}/api/detect-language`, {
          text: testCase.text
        });

        const detected = response.data.detectedLanguage;
        if (detected === testCase.expected) {
          this.log(`✅ "${testCase.text}" → ${detected}`, 'green');
          this.testResults.push({ 
            test: `Language Detection: ${testCase.text}`, 
            status: 'PASS', 
            expected: testCase.expected, 
            actual: detected 
          });
        } else {
          this.log(`❌ "${testCase.text}" → Expected: ${testCase.expected}, Got: ${detected}`, 'red');
          this.testResults.push({ 
            test: `Language Detection: ${testCase.text}`, 
            status: 'FAIL', 
            expected: testCase.expected, 
            actual: detected 
          });
        }
      } catch (error) {
        this.log(`❌ Language detection failed for "${testCase.text}": ${error.message}`, 'red');
        this.testResults.push({ 
          test: `Language Detection: ${testCase.text}`, 
          status: 'FAIL', 
          error: error.message 
        });
      }
    }
  }

  async testConversationFlow() {
    this.log('\n💬 Testing Conversation Flow and Memory...', 'yellow');
    
    const conversationFlow = [
      { message: 'Hello, I have been having headaches', expectedTopics: ['neurological'] },
      { message: 'They started last week', expectedContext: 'follow-up' },
      { message: 'What could be causing them?', expectedContext: 'follow-up' },
      { message: 'Are there any home remedies?', expectedContext: 'follow-up' }
    ];

    let conversationSessionId = `conversation_test_${Date.now()}`;

    for (let i = 0; i < conversationFlow.length; i++) {
      const testCase = conversationFlow[i];
      
      try {
        const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
          message: testCase.message,
          sessionId: conversationSessionId,
          userPreferences: { useRAG: true }
        });

        if (response.status === 200) {
          this.log(`✅ Message ${i + 1}: "${testCase.message}"`, 'green');
          this.log(`   Response length: ${response.data.responseLength} chars`, 'blue');
          this.log(`   Topics: ${response.data.topics?.join(', ') || 'none'}`, 'blue');
          
          this.testResults.push({ 
            test: `Conversation Flow Step ${i + 1}`, 
            status: 'PASS', 
            message: testCase.message,
            responseLength: response.data.responseLength,
            topics: response.data.topics
          });
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        this.log(`❌ Conversation step ${i + 1} failed: ${error.message}`, 'red');
        this.testResults.push({ 
          test: `Conversation Flow Step ${i + 1}`, 
          status: 'FAIL', 
          error: error.message 
        });
      }
    }

    // Test conversation history retrieval
    try {
      const historyResponse = await axios.get(`${this.baseURL}/api/chat-history/${conversationSessionId}`);
      
      if (historyResponse.status === 200 && historyResponse.data.history.length > 0) {
        this.log(`✅ Conversation history retrieved: ${historyResponse.data.history.length} messages`, 'green');
        this.log(`   Summary: ${historyResponse.data.summary}`, 'blue');
        this.testResults.push({ 
          test: 'Conversation History Retrieval', 
          status: 'PASS', 
          messageCount: historyResponse.data.history.length 
        });
      } else {
        throw new Error('No conversation history found');
      }
    } catch (error) {
      this.log(`❌ Conversation history retrieval failed: ${error.message}`, 'red');
      this.testResults.push({ 
        test: 'Conversation History Retrieval', 
        status: 'FAIL', 
        error: error.message 
      });
    }
  }

  async testWomensHealthMode() {
    this.log('\n👩‍⚕️ Testing Women\'s Health Mode...', 'yellow');
    
    const womensHealthQueries = [
      'I have irregular periods',
      'I think I might be pregnant',
      'I have questions about menopause',
      'I need information about breast health',
      'I have concerns about reproductive health'
    ];

    for (const query of womensHealthQueries) {
      try {
        const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
          message: query,
          sessionId: `womens_health_${Date.now()}`,
          medicalMode: 'general', // Should auto-detect and switch to women's mode
          userPreferences: { useRAG: true }
        });

        if (response.status === 200) {
          const isWomensMode = response.data.womensHealthMode;
          this.log(`✅ "${query}" → Women's mode: ${isWomensMode}`, isWomensMode ? 'green' : 'yellow');
          this.testResults.push({ 
            test: `Women's Health Detection: ${query}`, 
            status: isWomensMode ? 'PASS' : 'PARTIAL', 
            womensHealthMode: isWomensMode 
          });
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        this.log(`❌ Women's health test failed for "${query}": ${error.message}`, 'red');
        this.testResults.push({ 
          test: `Women's Health Detection: ${query}`, 
          status: 'FAIL', 
          error: error.message 
        });
      }
    }
  }

  async testMultiLanguageSupport() {
    this.log('\n🌐 Testing Multi-Language Support...', 'yellow');
    
    const multiLanguageTests = [
      { message: 'J\'ai mal à la tête', language: 'french', description: 'French headache query' },
      { message: 'My belly dey pain me', language: 'pidgin', description: 'Pidgin stomach pain query' },
      { message: 'Comment puis-je traiter la fièvre?', language: 'french', description: 'French fever treatment query' },
      { message: 'Wetin I fit do for cough?', language: 'pidgin', description: 'Pidgin cough query' }
    ];

    for (const test of multiLanguageTests) {
      try {
        const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
          message: test.message,
          sessionId: `multilang_${Date.now()}`,
          userPreferences: { useRAG: true }
        });

        if (response.status === 200) {
          const detectedLang = response.data.detectedLanguage;
          const sessionLang = response.data.sessionLanguage;
          
          this.log(`✅ ${test.description}`, 'green');
          this.log(`   Detected: ${detectedLang}, Session: ${sessionLang}`, 'blue');
          this.log(`   Response: ${response.data.response.substring(0, 100)}...`, 'blue');
          
          this.testResults.push({ 
            test: `Multi-Language: ${test.description}`, 
            status: 'PASS', 
            detectedLanguage: detectedLang,
            sessionLanguage: sessionLang
          });
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        this.log(`❌ Multi-language test failed for "${test.description}": ${error.message}`, 'red');
        this.testResults.push({ 
          test: `Multi-Language: ${test.description}`, 
          status: 'FAIL', 
          error: error.message 
        });
      }
    }
  }

  async testEnhancedRAG() {
    this.log('\n📚 Testing Enhanced RAG and Citations...', 'yellow');
    
    const ragQueries = [
      'What are the symptoms of diabetes?',
      'How is hypertension treated?',
      'What causes heart disease?'
    ];

    for (const query of ragQueries) {
      try {
        const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
          message: query,
          sessionId: `rag_test_${Date.now()}`,
          userPreferences: { 
            useRAG: true, 
            includeReferences: true,
            detailLevel: 'detailed'
          }
        });

        if (response.status === 200) {
          const usingRAG = response.data.usingRAG;
          const hasReferences = response.data.response.includes('[') && response.data.response.includes(']');
          
          this.log(`✅ "${query}" → RAG: ${usingRAG}, Citations: ${hasReferences}`, 'green');
          this.testResults.push({ 
            test: `Enhanced RAG: ${query}`, 
            status: 'PASS', 
            usingRAG: usingRAG,
            hasCitations: hasReferences
          });
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        this.log(`❌ RAG test failed for "${query}": ${error.message}`, 'red');
        this.testResults.push({ 
          test: `Enhanced RAG: ${query}`, 
          status: 'FAIL', 
          error: error.message 
        });
      }
    }
  }

  async testFileUpload() {
    this.log('\n📁 Testing File Upload Functionality...', 'yellow');
    
    // Create a test medical report file
    const testReportContent = `MEDICAL REPORT
Patient: Test Patient
Symptoms: Fever, headache, fatigue
Duration: 3 days
Recommendations: Rest, hydration, monitor temperature`;

    const testFilePath = path.join(__dirname, 'test_medical_report.txt');
    fs.writeFileSync(testFilePath, testReportContent);

    try {
      // Note: This is a simplified test - in a real scenario, you'd use FormData
      const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
        message: 'Please analyze this medical report',
        sessionId: `file_test_${Date.now()}`,
        files: [{
          name: 'test_medical_report.txt',
          content: testReportContent,
          type: 'text/plain',
          size: testReportContent.length
        }],
        userPreferences: { useRAG: true }
      });

      if (response.status === 200) {
        const filesProcessed = response.data.filesProcessed;
        this.log(`✅ File upload test → Files processed: ${filesProcessed}`, 'green');
        this.testResults.push({ 
          test: 'File Upload', 
          status: 'PASS', 
          filesProcessed: filesProcessed 
        });
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      this.log(`❌ File upload test failed: ${error.message}`, 'red');
      this.testResults.push({ 
        test: 'File Upload', 
        status: 'FAIL', 
        error: error.message 
      });
    } finally {
      // Clean up test file
      if (fs.existsSync(testFilePath)) {
        fs.unlinkSync(testFilePath);
      }
    }
  }

  generateTestReport() {
    this.log('\n📋 Test Report Summary', 'cyan');
    this.log('=' .repeat(60), 'cyan');

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    const partialTests = this.testResults.filter(r => r.status === 'PARTIAL').length;

    this.log(`Total Tests: ${totalTests}`, 'bright');
    this.log(`Passed: ${passedTests}`, 'green');
    this.log(`Failed: ${failedTests}`, 'red');
    this.log(`Partial: ${partialTests}`, 'yellow');
    this.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 'bright');

    // Save detailed report to file
    const reportPath = path.join(__dirname, `test_report_${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: { totalTests, passedTests, failedTests, partialTests },
      results: this.testResults
    }, null, 2));

    this.log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');
  }

  async interactiveMode() {
    this.log('\n🎮 Interactive Testing Mode', 'cyan');
    this.log('Type your messages to test the chatbot. Type "exit" to quit.\n', 'yellow');

    const askQuestion = () => {
      this.rl.question('You: ', async (input) => {
        if (input.toLowerCase() === 'exit') {
          this.rl.close();
          return;
        }

        try {
          const response = await axios.post(`${this.baseURL}/api/medical-chat`, {
            message: input,
            sessionId: this.sessionId,
            userPreferences: { useRAG: true }
          });

          this.log(`\nQala-Lwazi: ${response.data.response}`, 'green');
          this.log(`\nLanguage: ${response.data.detectedLanguage} | Mode: ${response.data.medicalMode} | RAG: ${response.data.usingRAG}\n`, 'blue');
        } catch (error) {
          this.log(`Error: ${error.message}`, 'red');
        }

        askQuestion();
      });
    };

    askQuestion();
  }

  async run() {
    const args = process.argv.slice(2);
    
    if (args.includes('--interactive') || args.includes('-i')) {
      await this.interactiveMode();
    } else {
      await this.runAllTests();
      this.rl.close();
    }
  }
}

// Run the tester
if (require.main === module) {
  const tester = new CareAITester();
  tester.run().catch(console.error);
}

module.exports = CareAITester;
