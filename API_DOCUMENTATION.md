# CareAI Medical Chatbot - API Documentation

## Base URL
```
http://localhost:3001
```

## Authentication
Currently, no authentication is required. All endpoints are publicly accessible.

## Content Type
All POST requests should use `Content-Type: application/json` unless specified otherwise.

---

## Endpoints

### 1. Health Check

**GET** `/health`

Check server status and available features.

#### Response
```json
{
  "status": "ok",
  "message": "Qala-Lwazi Medical Assistant API is running",
  "features": {
    "multiLanguage": true,
    "conversationMemory": true,
    "womensHealthMode": true,
    "enhancedRAG": true
  }
}
```

---

### 2. Language Detection

**POST** `/api/detect-language`

Detect the language of input text and get appropriate greeting.

#### Request Body
```json
{
  "text": "Hello, how are you?"
}
```

#### Response
```json
{
  "detectedLanguage": "english",
  "greeting": "Hello! I'm <PERSON><PERSON><PERSON>, your medical assistant. How can I help you with your health questions today?",
  "supportedLanguages": ["english", "french", "pidgin"]
}
```

#### Supported Languages
- **english**: Standard English
- **french**: French language
- **pidgin**: Pidgin English (Cameroon and West African variant)

---

### 3. Medical Chat

**POST** `/api/medical-chat`

Main endpoint for medical consultations with enhanced features.

#### Request Body
```json
{
  "message": "I have been having headaches for the past week",
  "sessionId": "optional_session_id",
  "medicalMode": "general",
  "systemPrompt": "optional_custom_prompt",
  "userPreferences": {
    "useRAG": true,
    "detailLevel": "detailed",
    "creativity": "balanced",
    "responseLength": "medium",
    "includeReferences": true
  },
  "files": []
}
```

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `message` | string | Yes | User's medical question or concern |
| `sessionId` | string | No | Session identifier for conversation continuity |
| `medicalMode` | string | No | `"general"` or `"women"` (auto-detected if not specified) |
| `systemPrompt` | string | No | Custom system prompt override |
| `userPreferences` | object | No | User preferences for response customization |
| `files` | array | No | Uploaded files for analysis |

#### User Preferences

| Preference | Type | Options | Default | Description |
|------------|------|---------|---------|-------------|
| `useRAG` | boolean | true/false | true | Enable RAG system for enhanced responses |
| `detailLevel` | string | "simple", "medium", "detailed" | "medium" | Response complexity level |
| `creativity` | string | "conservative", "balanced", "creative" | "balanced" | AI creativity level |
| `responseLength` | string | "short", "medium", "long" | "medium" | Response length preference |
| `includeReferences` | boolean | true/false | true | Include citations and references |

#### Response
```json
{
  "response": "Qala-Lwazi is thinking...\n\n**Headache Analysis**\n\nBased on your description of headaches lasting a week, here are some possible considerations:\n\n**Common Causes:**\n- Tension headaches [1]\n- Dehydration [2]\n- Stress or lack of sleep [3]\n\n**References:**\n[1] Medical Handbook - Neurological Conditions (Relevance: 94.5%)\n[2] Clinical Guidelines - Headache Management (Relevance: 87.2%)",
  "sessionId": "session_**********",
  "historyLength": 2,
  "usingRAG": true,
  "medicalMode": "general",
  "originalMedicalMode": "general",
  "womensHealthMode": false,
  "detectedLanguage": "english",
  "sessionLanguage": "english",
  "filesProcessed": 0,
  "responseLength": 1245,
  "conversationSummary": "Patient reporting headaches lasting one week, seeking medical guidance",
  "topics": ["neurological", "pain management"]
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `response` | string | AI-generated medical response |
| `sessionId` | string | Session identifier for conversation continuity |
| `historyLength` | number | Number of messages in conversation history |
| `usingRAG` | boolean | Whether RAG system was used |
| `medicalMode` | string | Effective medical mode used |
| `originalMedicalMode` | string | Originally requested medical mode |
| `womensHealthMode` | boolean | Whether women's health mode was activated |
| `detectedLanguage` | string | Detected language of input message |
| `sessionLanguage` | string | Language used for response |
| `filesProcessed` | number | Number of files processed |
| `responseLength` | number | Character length of response |
| `conversationSummary` | string | Summary of conversation context |
| `topics` | array | Identified medical topics |

---

### 4. File Upload

**POST** `/api/upload-files`

Upload medical documents for analysis.

#### Content Type
`multipart/form-data`

#### Request
```
files: [file1.pdf, file2.txt, ...]
```

#### Supported File Types
- **Images**: JPEG, PNG, GIF, WebP
- **Documents**: PDF, TXT, CSV, DOC, DOCX
- **Media**: MP3, WAV, MP4 (limited support)

#### File Size Limits
- Maximum file size: 10MB per file
- Maximum files per request: 5

#### Response
```json
{
  "success": true,
  "files": [
    {
      "id": **********.123,
      "name": "medical_report.pdf",
      "size": 245760,
      "type": "application/pdf",
      "geminiUri": "local://medical_report.pdf",
      "geminiName": "medical_report.pdf",
      "content": "MEDICAL REPORT\nPatient: John Doe...",
      "processed": true,
      "contentLength": 2048
    }
  ],
  "message": "Successfully uploaded 1 files"
}
```

---

### 5. Conversation History

#### Get Conversation History

**GET** `/api/chat-history/:sessionId`

Retrieve enhanced conversation history with context.

#### Response
```json
{
  "sessionId": "session_**********",
  "history": [
    {
      "role": "user",
      "parts": [{"text": "I have been having headaches"}],
      "timestamp": "2024-01-15T10:30:00.000Z",
      "messageType": "user",
      "topics": ["neurological"],
      "sentiment": "negative",
      "medicalTerms": ["headaches"],
      "isFollowUp": false
    }
  ],
  "summary": "Patient reporting headaches, seeking medical guidance",
  "topics": ["neurological", "pain management"],
  "language": "english",
  "userProfile": {
    "preferredLanguage": "english",
    "medicalHistory": [],
    "commonConcerns": ["headaches"],
    "communicationStyle": "formal",
    "urgencyLevel": "normal"
  },
  "lastActivity": "2024-01-15T10:35:00.000Z"
}
```

#### Clear Conversation History

**DELETE** `/api/chat-history/:sessionId`

Clear conversation history for a specific session.

#### Response
```json
{
  "success": true,
  "message": "Conversation history cleared"
}
```

---

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error message description",
  "details": "Additional error details (optional)"
}
```

### Common HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input parameters |
| 500 | Internal Server Error - Server-side error |

---

## Language-Specific Examples

### English Query
```json
{
  "message": "What are the symptoms of diabetes?",
  "userPreferences": {"useRAG": true}
}
```

### French Query
```json
{
  "message": "Quels sont les symptômes du diabète?",
  "userPreferences": {"useRAG": true}
}
```

### Pidgin English Query
```json
{
  "message": "Wetin be the signs of sugar sickness?",
  "userPreferences": {"useRAG": true}
}
```

---

## Women's Health Mode Examples

### Automatic Activation
```json
{
  "message": "I have irregular periods",
  "medicalMode": "general"
}
```

Response will include:
```json
{
  "womensHealthMode": true,
  "medicalMode": "women"
}
```

### Manual Activation
```json
{
  "message": "I have questions about my health",
  "medicalMode": "women"
}
```

---

## RAG and Citation Examples

### With RAG Enabled
```json
{
  "message": "How is hypertension treated?",
  "userPreferences": {
    "useRAG": true,
    "includeReferences": true
  }
}
```

Response includes citations:
```
Treatment for hypertension typically includes [1] lifestyle modifications and [2] medication management...

**References:**
[1] Medical Handbook - Cardiovascular Disorders (Relevance: 96.3%)
[2] Clinical Guidelines - Hypertension Management (Relevance: 91.8%)
```

---

## Best Practices

1. **Session Management**: Always use consistent `sessionId` for conversation continuity
2. **Language Detection**: Let the system auto-detect language for best results
3. **File Uploads**: Upload files first, then reference them in chat messages
4. **Error Handling**: Always handle potential error responses
5. **Rate Limiting**: Implement client-side rate limiting for production use

---

## Rate Limits

Currently, no rate limits are enforced, but it's recommended to:
- Limit to 60 requests per minute per session
- Avoid concurrent requests from the same session
- Implement exponential backoff for error responses
