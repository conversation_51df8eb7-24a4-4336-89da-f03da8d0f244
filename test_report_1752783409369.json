{"timestamp": "2025-07-17T20:16:49.369Z", "summary": {"totalTests": 25, "passedTests": 24, "failedTests": 0, "partialTests": 1}, "results": [{"test": "Server Health", "status": "PASS", "details": {"status": "ok", "message": "Qala-Lwazi Medical Assistant API is running", "features": {"multiLanguage": true, "conversationMemory": true, "womensHealthMode": true, "enhancedRAG": true}}}, {"test": "Language Detection: Hello, how are you?", "status": "PASS", "expected": "english", "actual": "english"}, {"test": "Language Detection: <PERSON><PERSON><PERSON>, comment allez-vous?", "status": "PASS", "expected": "french", "actual": "french"}, {"test": "Language Detection: How far? Wetin dey happen?", "status": "PASS", "expected": "pidgin", "actual": "pidgin"}, {"test": "Language Detection: I have a headache", "status": "PASS", "expected": "english", "actual": "english"}, {"test": "Language Detection: J'ai mal à la tête", "status": "PASS", "expected": "french", "actual": "french"}, {"test": "Language Detection: My head dey pain me", "status": "PASS", "expected": "pidgin", "actual": "pidgin"}, {"test": "Conversation Flow Step 1", "status": "PASS", "message": "Hello, I have been having headaches", "responseLength": 2950, "topics": ["neurological"]}, {"test": "Conversation Flow Step 2", "status": "PASS", "message": "They started last week", "responseLength": 2042, "topics": ["neurological", "digestive"]}, {"test": "Conversation Flow Step 3", "status": "PASS", "message": "What could be causing them?", "responseLength": 2656, "topics": ["neurological", "digestive"]}, {"test": "Conversation Flow Step 4", "status": "PASS", "message": "Are there any home remedies?", "responseLength": 2299, "topics": ["neurological", "digestive"]}, {"test": "Conversation History Retrieval", "status": "PASS", "messageCount": 8}, {"test": "Women's Health Detection: I have irregular periods", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I think I might be pregnant", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I have questions about menopause", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I need information about breast health", "status": "PASS", "womensHealthMode": true}, {"test": "Women's Health Detection: I have concerns about reproductive health", "status": "PARTIAL", "womensHealthMode": false}, {"test": "Multi-Language: French headache query", "status": "PASS", "detectedLanguage": "french", "sessionLanguage": "french"}, {"test": "Multi-Language: Pidgin stomach pain query", "status": "PASS", "detectedLanguage": "pidgin", "sessionLanguage": "pidgin"}, {"test": "Multi-Language: French fever treatment query", "status": "PASS", "detectedLanguage": "french", "sessionLanguage": "french"}, {"test": "Multi-Language: Pidgin cough query", "status": "PASS", "detectedLanguage": "pidgin", "sessionLanguage": "pidgin"}, {"test": "Enhanced RAG: What are the symptoms of diabetes?", "status": "PASS", "usingRAG": true, "hasCitations": true}, {"test": "Enhanced RAG: How is hypertension treated?", "status": "PASS", "usingRAG": true, "hasCitations": true}, {"test": "Enhanced RAG: What causes heart disease?", "status": "PASS", "usingRAG": true, "hasCitations": true}, {"test": "File Upload", "status": "PASS", "filesProcessed": 1}]}