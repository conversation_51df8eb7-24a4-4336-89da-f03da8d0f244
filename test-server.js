const express = require('express');
const cors = require('cors');

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Simple test endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Test server is running' });
});

// Test medical chat endpoint without file upload
app.post('/api/medical-chat', async (req, res) => {
  try {
    const { message, medicalMode = 'general' } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Simple mock response for testing
    const mockResponse = `Hello! I am Qala-Lwazi in ${medicalMode} mode. You asked: "${message}". This is a test response to verify the server is working.`;

    res.json({
      response: mockResponse,
      sessionId: `test_session_${Date.now()}`,
      medicalMode: medicalMode,
      filesProcessed: 0,
      responseLength: mockResponse.length
    });

  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({
      error: 'Failed to generate response',
      details: error.message
    });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Test server running on port ${port}`);
});
