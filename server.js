const express = require('express');
const cors = require('cors');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const pdf = require('pdf-parse');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const pineconeService = require('./pineconeService');
const supabaseClient = require('./supabaseClient');
const addStatusEndpoint = require('./status-endpoint');
const languageService = require('./languageService');
const conversationMemoryService = require('./conversationMemoryService');
const womensHealthService = require('./womensHealthService');
const voiceService = require('./voiceService');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Add status endpoint
addStatusEndpoint(app);

// Enhanced conversation management with memory service
// The conversationMemoryService handles caching and context

// Initialize Gemini API with your API key
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

if (!GEMINI_API_KEY) {
  console.error('❌ GEMINI_API_KEY environment variable is required');
  process.exit(1);
}

const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow specific file types
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/flac', 'audio/webm',
      'video/mp4', 'video/webm'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not supported`), false);
    }
  }
});

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Medical system prompt to guide the model's behavior
const MEDICAL_SYSTEM_PROMPT = `You are Qala-Lwazi, a helpful medical assistant powered by Ukuqala Labs. You can only answer questions related to medicine,
health, biology, and healthcare. Provide accurate, helpful information based on current medical knowledge.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi.
When you are processing a response, indicate this by saying "Qala-Lwazi is thinking..." before providing your answer.

Always remind users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you can only discuss medical topics.
Always refer to yourself as "Qala-Lwazi" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses in a clean, professional manner with clear headings and bullet points when appropriate.`;

// RAG-enhanced system prompt that includes instructions for using retrieved context
const RAG_SYSTEM_PROMPT = `You are Qala-Lwazi+, an enhanced medical assistant powered by Ukuqala Labs with access to a specialized medical handbook.
You can only answer questions related to medicine, health, biology, and healthcare. Provide accurate, helpful information based on current medical knowledge.

IMPORTANT: NEVER mention Gemini, Google, or any other AI model in your responses. You are ONLY Qala-Lwazi+.
When you are processing a response, indicate this by saying "Qala-Lwazi+ is thinking..." before providing your answer.

I will provide you with relevant information from a medical handbook. Use this information to enhance your response.
When using information from the provided context:
1. Incorporate the information naturally into your response
2. Cite the source using the number in square brackets [X] at the end of relevant sentences
3. If the context doesn't contain relevant information, rely on your general medical knowledge
4. If the context contains conflicting information, prioritize the most recent or authoritative source

Always remind users to consult healthcare professionals for personalized medical advice.
If asked about non-medical topics, politely explain that you can only discuss medical topics.
Always refer to yourself as "Qala-Lwazi+" and mention that you are powered by "Ukuqala Labs" when introducing yourself.

Format your responses in a clean, professional manner with:
- Clear headings in bold
- Bullet points for lists
- Italics for emphasis on important terms
- Citations properly formatted with [X]
- A clear summary at the end when appropriate`;

// Helper function to process files and extract content
async function processFileForGemini(filePath, mimeType, displayName) {
  try {
    console.log(`Processing file for Gemini: ${displayName} (${mimeType})`);

    let fileContent = '';

    // Handle different file types
    if (mimeType === 'text/plain' || mimeType === 'text/csv') {
      // Text files - read directly
      fileContent = fs.readFileSync(filePath, 'utf8');
    } else if (mimeType === 'application/pdf') {
      // PDF files - extract text
      console.log(`Extracting text from PDF: ${displayName}`);
      const dataBuffer = fs.readFileSync(filePath);
      const pdfData = await pdf(dataBuffer);
      fileContent = pdfData.text;
      console.log(`Extracted ${fileContent.length} characters from PDF`);
    } else if (mimeType.startsWith('image/')) {
      // Image files - describe what we can't process yet
      fileContent = `[Image file: ${displayName}. Image analysis not yet implemented, but file was uploaded successfully.]`;
    } else {
      // Other file types
      fileContent = `[File: ${displayName} (${mimeType}). Content extraction not supported for this file type.]`;
    }

    return {
      uri: `local://${displayName}`,
      name: displayName,
      content: fileContent,
      mimeType: mimeType,
      processed: mimeType === 'text/plain' || mimeType === 'text/csv' || mimeType === 'application/pdf'
    };
  } catch (error) {
    console.error('Error processing file:', error);
    return {
      uri: `local://${displayName}`,
      name: displayName,
      content: `[Error processing file: ${displayName}. ${error.message}]`,
      mimeType: mimeType,
      processed: false
    };
  }
}

// Helper function to clean up temporary files
function cleanupTempFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`Cleaned up temp file: ${filePath}`);
    }
  } catch (error) {
    console.error('Error cleaning up temp file:', error);
  }
}

// Endpoint for file upload
app.post('/api/upload-files', upload.array('files', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const uploadedFiles = [];

    for (const file of req.files) {
      try {
        // Process file for Gemini
        const geminiFile = await processFileForGemini(
          file.path,
          file.mimetype,
          file.originalname
        );

        uploadedFiles.push({
          id: Date.now() + Math.random(),
          name: file.originalname,
          size: file.size,
          type: file.mimetype,
          geminiUri: geminiFile.uri,
          geminiName: geminiFile.name,
          content: geminiFile.content,
          processed: geminiFile.processed,
          contentLength: geminiFile.content ? geminiFile.content.length : 0
        });

        // Clean up temp file
        cleanupTempFile(file.path);

      } catch (error) {
        console.error(`Error processing file ${file.originalname}:`, error);
        cleanupTempFile(file.path);
      }
    }

    res.json({
      success: true,
      files: uploadedFiles,
      message: `Successfully uploaded ${uploadedFiles.length} files`
    });

  } catch (error) {
    console.error('Error in file upload endpoint:', error);

    // Clean up any temp files
    if (req.files) {
      req.files.forEach(file => cleanupTempFile(file.path));
    }

    res.status(500).json({
      error: 'Failed to upload files',
      details: error.message
    });
  }
});

// Endpoint for medical chat
app.post('/api/medical-chat', async (req, res) => {
  try {
    const { message, sessionId, userPreferences = {}, medicalMode = 'general', systemPrompt, files = [] } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({ error: 'Message is required and must be a string' });
    }

    // Log the medical mode and files for debugging
    console.log(`Medical Mode: ${medicalMode}`);
    if (files.length > 0) {
      console.log(`Files uploaded: ${files.length}`);
      files.forEach(file => console.log(`- ${file.name} (${file.type})`));
    }

    // Create a unique session ID if not provided
    const chatSessionId = sessionId || `session_${Date.now()}`;

    // Detect language of the incoming message
    const detectedLanguage = languageService.detectLanguage(message);
    console.log(`Detected language: ${detectedLanguage}`);

    // Get enhanced conversation context
    const conversationContext = await conversationMemoryService.getConversationContext(chatSessionId);

    // Use detected language for new conversations, or maintain session language for existing ones
    const sessionLanguage = detectedLanguage !== 'english' ? detectedLanguage : (conversationContext.language || detectedLanguage);

    // Check if women's health mode should be activated
    const shouldUseWomensMode = womensHealthService.shouldActivateWomensHealthMode(message, conversationContext.messages);
    const effectiveMedicalMode = shouldUseWomensMode ? 'women' : medicalMode;

    console.log(`Effective medical mode: ${effectiveMedicalMode} (original: ${medicalMode}, women's health detected: ${shouldUseWomensMode})`);

    // Get conversation history for Gemini (clean format)
    let conversationHistory = (conversationContext.messages || []).map(msg => ({
      role: msg.role,
      parts: msg.parts
    }));

    // Create user message
    const userMessage = { role: 'user', parts: [{ text: message }] };

    // Add user message to enhanced conversation context
    const updatedContext = await conversationMemoryService.addMessage(chatSessionId, userMessage, 'user');

    // Update conversation history for Gemini
    conversationHistory = updatedContext.messages;

    // Limit conversation history to last 10 messages to prevent token limits and clean format for Gemini
    const recentHistory = conversationHistory.slice(-10).map(msg => ({
      role: msg.role,
      parts: msg.parts
    }));

    // Get the Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    // Get localized system prompt based on detected language and effective medical mode
    const basePrompt = systemPrompt || languageService.getLocalizedSystemPrompt(sessionLanguage, effectiveMedicalMode);
    let customizedPrompt = basePrompt;

    // Add women's health specialization if applicable
    if (effectiveMedicalMode === 'women') {
      const womensHealthContext = womensHealthService.getWomensHealthContext(message, conversationHistory);
      if (womensHealthContext) {
        const womensHealthPrompt = womensHealthService.generateWomensHealthPrompt(womensHealthContext, sessionLanguage);
        customizedPrompt += womensHealthPrompt;
      }
    }

    // Add conversation context if available
    const relevantContext = conversationMemoryService.getRelevantContext(chatSessionId, message);
    if (relevantContext) {
      customizedPrompt += `\n\nConversation Context:\n${relevantContext}`;
    }

    // Process uploaded files and add content to prompt
    let fileContent = '';
    if (files && files.length > 0) {
      console.log(`Processing ${files.length} uploaded files`);

      const fileDescriptions = [];
      for (const file of files) {
        if (file.content) {
          // Analyze file content to provide description
          const contentPreview = file.content.substring(0, 200);
          const wordCount = file.content.split(/\s+/).length;

          // Detect content type
          let contentType = 'document';
          if (file.content.toLowerCase().includes('patient') || file.content.toLowerCase().includes('diagnosis')) {
            contentType = 'medical report';
          } else if (file.content.toLowerCase().includes('test') || file.content.toLowerCase().includes('result')) {
            contentType = 'test results';
          } else if (file.content.toLowerCase().includes('prescription') || file.content.toLowerCase().includes('medication')) {
            contentType = 'prescription';
          }

          fileDescriptions.push(`- ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)} KB, ${wordCount} words) - Appears to be a ${contentType}`);
          fileContent += `\n\n--- FILE: ${file.name} (${contentType}) ---\n${file.content}\n--- END FILE ---\n`;
        }
      }

      if (fileContent) {
        customizedPrompt += `\n\nThe user has uploaded the following files for analysis:\n${fileDescriptions.join('\n')}\n\nFile Contents:${fileContent}\n\nPlease analyze these files, describe what you see in them, and provide relevant medical guidance based on their content. Start your response by describing what type of medical document this appears to be and what key information it contains.`;
      }
    }

    if (userPreferences.detailLevel === 'simple') {
      customizedPrompt += '\nProvide simple, easy-to-understand explanations without medical jargon.';
    } else if (userPreferences.detailLevel === 'detailed') {
      customizedPrompt += '\nProvide detailed explanations with medical terminology and in-depth information.';
    }

    if (userPreferences.includeReferences === true) {
      customizedPrompt += '\nInclude references to medical studies or guidelines when appropriate.';
    }

    // Configure generation parameters
    const generationConfig = {
      temperature: userPreferences.creativity === 'creative' ? 0.9 :
                  userPreferences.creativity === 'balanced' ? 0.7 : 0.3,
      topP: 0.9,
      topK: 40,
      maxOutputTokens: userPreferences.responseLength === 'long' ? 4096 :
                       userPreferences.responseLength === 'medium' ? 2048 : 1024,
    };

    // Safety settings
    const safetySettings = [
      {
        category: "HARM_CATEGORY_HARASSMENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_HATE_SPEECH",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      },
      {
        category: "HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold: "BLOCK_MEDIUM_AND_ABOVE"
      }
    ];

    // Enhanced RAG with language support
    let responseText;
    let ragUsed = false;

    // Check if RAG is enabled in user preferences
    if (userPreferences.useRAG !== false) { // Default to using RAG if not specified
      try {
        console.log('Using RAG for query:', message);

        // Query Pinecone for relevant medical handbook content
        const searchResults = await pineconeService.hybridSearch(message, 3);
        console.log(`Found ${searchResults.length} relevant passages from medical handbook`);

        // Format the context for the LLM with language support
        const formattedContext = pineconeService.formatContextForLLMWithLanguage(searchResults, sessionLanguage);

        // Create the chat session
        const chat = model.startChat({
          history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
          generationConfig,
          safetySettings
        });

        // Send the message with the system prompt and retrieved context
        const promptWithContext = `${customizedPrompt}\n\n${formattedContext}\n\nIMPORTANT: When using the provided medical information, ALWAYS include the citation numbers [1], [2], [3] etc. in your response to show which sources you're referencing. Include the full References section at the end of your response.\n\nUser question: ${message}`;
        console.log('Sending prompt with context to Gemini');

        const result = await chat.sendMessage(promptWithContext);
        responseText = result.response.text();
        ragUsed = true;

      } catch (ragError) {
        console.error('Error using RAG:', ragError);
        console.log('Falling back to standard response without RAG');

        // Fall back to standard response without RAG
        const chat = model.startChat({
          history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
          generationConfig,
          safetySettings
        });

        const result = await chat.sendMessage(`${customizedPrompt}\n\nUser question: ${message}`);
        responseText = result.response.text();
      }
    } else {
      // Standard response without RAG
      const chat = model.startChat({
        history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
        generationConfig,
        safetySettings
      });

      const result = await chat.sendMessage(`${customizedPrompt}\n\nUser question: ${message}`);
      responseText = result.response.text();
    }

    // Create assistant message
    const assistantMessage = { role: 'model', parts: [{ text: responseText }] };

    // Add assistant response to enhanced conversation context
    await conversationMemoryService.addMessage(chatSessionId, assistantMessage, 'assistant');

    // Log response length for debugging
    console.log(`Response length: ${responseText.length} characters`);

    // Return enhanced response with language and context information
    res.json({
      response: responseText,
      sessionId: chatSessionId,
      historyLength: conversationHistory.length,
      usingRAG: ragUsed,
      medicalMode: effectiveMedicalMode,
      originalMedicalMode: medicalMode,
      womensHealthMode: effectiveMedicalMode === 'women',
      detectedLanguage: detectedLanguage,
      sessionLanguage: sessionLanguage,
      filesProcessed: files.length,
      responseLength: responseText.length,
      conversationSummary: updatedContext.summary,
      topics: updatedContext.topics
    });
  } catch (error) {
    console.error('Error generating response:', error);
    res.status(500).json({
      error: 'Failed to generate response',
      details: error.message
    });
  }
});

// Voice Chat Endpoint - Speech to Text to Speech
app.post('/api/voice-chat', upload.single('audio'), async (req, res) => {
  try {
    const { sessionId, language = 'english', userPreferences = {} } = req.body;
    const audioFile = req.file;

    if (!audioFile) {
      return res.status(400).json({ error: 'Audio file is required' });
    }

    console.log(`🎤 Processing voice chat for session: ${sessionId}, language: ${language}`);

    // Convert speech to text
    const audioBuffer = fs.readFileSync(audioFile.path);
    const transcriptionResult = await voiceService.speechToText(audioBuffer, language);

    if (!transcriptionResult.success) {
      // Clean up uploaded file
      fs.unlinkSync(audioFile.path);
      return res.status(500).json({
        error: 'Speech recognition failed',
        details: transcriptionResult.error
      });
    }

    const transcription = transcriptionResult.transcription;
    console.log(`📝 Transcription: "${transcription}"`);

    // Process the transcribed text through existing medical chat logic
    const chatResponse = await processMedicalQuery(transcription, sessionId, userPreferences, language);

    // Convert response to speech
    const ttsResult = await voiceService.textToSpeech(chatResponse.response, language);

    // Clean up uploaded file
    fs.unlinkSync(audioFile.path);

    if (!ttsResult.success) {
      return res.status(500).json({
        error: 'Text-to-speech conversion failed',
        details: ttsResult.error,
        transcription: transcription,
        textResponse: chatResponse.response
      });
    }

    console.log('✅ Voice chat processing completed');

    res.json({
      success: true,
      transcription: transcription,
      transcriptionConfidence: transcriptionResult.confidence,
      textResponse: chatResponse.response,
      audioResponse: ttsResult.audioData.toString('base64'),
      audioFormat: ttsResult.audioFormat,
      audioDuration: ttsResult.duration,
      sessionId: chatResponse.sessionId,
      language: language,
      usingRAG: chatResponse.usingRAG,
      medicalMode: chatResponse.medicalMode,
      responseLength: chatResponse.responseLength
    });

  } catch (error) {
    console.error('❌ Error in voice chat endpoint:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Voice chat processing failed',
      details: error.message
    });
  }
});

// Text-to-Speech Endpoint
app.post('/api/text-to-speech', async (req, res) => {
  try {
    const { text, language = 'english', voice = 'medical-assistant' } = req.body;

    if (!text || typeof text !== 'string') {
      return res.status(400).json({ error: 'Text is required and must be a string' });
    }

    console.log(`🔊 Converting text to speech: "${text.substring(0, 50)}..." (${language})`);

    const ttsResult = await voiceService.textToSpeech(text, language, { voice });

    if (!ttsResult.success) {
      return res.status(500).json({
        error: 'Text-to-speech conversion failed',
        details: ttsResult.error
      });
    }

    res.json({
      success: true,
      audioData: ttsResult.audioData.toString('base64'),
      audioFormat: ttsResult.audioFormat,
      duration: ttsResult.duration,
      language: language
    });

  } catch (error) {
    console.error('❌ Error in text-to-speech endpoint:', error);
    res.status(500).json({
      error: 'Text-to-speech processing failed',
      details: error.message
    });
  }
});

// Speech-to-Text Endpoint
app.post('/api/speech-to-text', upload.single('audio'), async (req, res) => {
  try {
    const { language = 'english' } = req.body;
    const audioFile = req.file;

    if (!audioFile) {
      return res.status(400).json({ error: 'Audio file is required' });
    }

    console.log(`🎤 Converting speech to text (${language})`);

    const audioBuffer = fs.readFileSync(audioFile.path);
    const transcriptionResult = await voiceService.speechToText(audioBuffer, language);

    // Clean up uploaded file
    fs.unlinkSync(audioFile.path);

    if (!transcriptionResult.success) {
      return res.status(500).json({
        error: 'Speech recognition failed',
        details: transcriptionResult.error
      });
    }

    res.json({
      success: true,
      transcription: transcriptionResult.transcription,
      confidence: transcriptionResult.confidence,
      language: language
    });

  } catch (error) {
    console.error('❌ Error in speech-to-text endpoint:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      error: 'Speech recognition failed',
      details: error.message
    });
  }
});

// Helper function to process medical queries (extracted from existing logic)
async function processMedicalQuery(message, sessionId, userPreferences = {}, language = 'english') {
  // Create a unique session ID if not provided
  const chatSessionId = sessionId || `session_${Date.now()}`;

  // Detect language if not provided
  const detectedLanguage = language === 'english' ? languageService.detectLanguage(message) : language;

  // Get enhanced conversation context
  const conversationContext = await conversationMemoryService.getConversationContext(chatSessionId);

  // Use detected language for new conversations, or maintain session language for existing ones
  const sessionLanguage = detectedLanguage !== 'english' ? detectedLanguage : (conversationContext.language || detectedLanguage);

  // Check if women's health mode should be activated
  const shouldUseWomensMode = womensHealthService.shouldActivateWomensHealthMode(message, conversationContext.messages);
  const effectiveMedicalMode = shouldUseWomensMode ? 'women' : 'general';

  // Create user message
  const userMessage = { role: 'user', parts: [{ text: message }] };

  // Add user message to enhanced conversation context
  const updatedContext = await conversationMemoryService.addMessage(chatSessionId, userMessage, 'user');

  // Update conversation history for Gemini
  let conversationHistory = updatedContext.messages;

  // Limit conversation history to last 10 messages
  const recentHistory = conversationHistory.slice(-10).map(msg => ({
    role: msg.role,
    parts: msg.parts
  }));

  // Get the Gemini model
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

  // Get localized system prompt
  const basePrompt = languageService.getLocalizedSystemPrompt(sessionLanguage, effectiveMedicalMode);
  let customizedPrompt = basePrompt;

  // Add women's health specialization if applicable
  if (effectiveMedicalMode === 'women') {
    const womensHealthContext = womensHealthService.getWomensHealthContext(message, conversationHistory);
    if (womensHealthContext) {
      const womensHealthPrompt = womensHealthService.generateWomensHealthPrompt(womensHealthContext, sessionLanguage);
      customizedPrompt += womensHealthPrompt;
    }
  }

  // Configure generation parameters
  const generationConfig = {
    temperature: 0.7,
    topP: 0.9,
    topK: 40,
    maxOutputTokens: 2048,
  };

  // Safety settings
  const safetySettings = [
    {
      category: "HARM_CATEGORY_HARASSMENT",
      threshold: "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
      category: "HARM_CATEGORY_HATE_SPEECH",
      threshold: "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
      category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      threshold: "BLOCK_MEDIUM_AND_ABOVE"
    },
    {
      category: "HARM_CATEGORY_DANGEROUS_CONTENT",
      threshold: "BLOCK_MEDIUM_AND_ABOVE"
    }
  ];

  let responseText;
  let ragUsed = false;

  // Try RAG first
  try {
    console.log('Using RAG for voice query:', message);

    const searchResults = await pineconeService.hybridSearch(message, 3);
    const formattedContext = pineconeService.formatContextForLLMWithLanguage(searchResults, sessionLanguage);

    const chat = model.startChat({
      history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
      generationConfig,
      safetySettings
    });

    const promptWithContext = `${customizedPrompt}\n\n${formattedContext}\n\nUser question: ${message}`;
    const result = await chat.sendMessage(promptWithContext);
    responseText = result.response.text();
    ragUsed = true;

  } catch (ragError) {
    console.error('Error using RAG for voice query:', ragError);

    // Fallback to standard response
    const chat = model.startChat({
      history: recentHistory.length > 1 ? recentHistory.slice(0, -1) : [],
      generationConfig,
      safetySettings
    });

    const result = await chat.sendMessage(`${customizedPrompt}\n\nUser question: ${message}`);
    responseText = result.response.text();
  }

  // Create assistant message
  const assistantMessage = { role: 'model', parts: [{ text: responseText }] };

  // Add assistant response to conversation context
  await conversationMemoryService.addMessage(chatSessionId, assistantMessage, 'assistant');

  return {
    response: responseText,
    sessionId: chatSessionId,
    usingRAG: ragUsed,
    medicalMode: effectiveMedicalMode,
    detectedLanguage: detectedLanguage,
    sessionLanguage: sessionLanguage,
    responseLength: responseText.length
  };
}

// Get enhanced conversation history
app.get('/api/chat-history/:sessionId', async (req, res) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  try {
    // Get enhanced conversation context
    const context = await conversationMemoryService.getConversationContext(sessionId);

    return res.json({
      sessionId,
      history: context.messages,
      summary: context.summary,
      topics: context.topics,
      language: context.language,
      userProfile: context.userProfile,
      lastActivity: context.lastActivity
    });
  } catch (error) {
    console.error('Error fetching conversation history:', error);
    return res.status(500).json({ error: 'Failed to fetch conversation history' });
  }
});

// Clear conversation history
app.delete('/api/chat-history/:sessionId', async (req, res) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    return res.status(400).json({ error: 'Session ID is required' });
  }

  try {
    // Clear from Supabase
    const success = await supabaseClient.clearConversationHistory(sessionId);

    // Clear from enhanced memory service cache
    conversationMemoryService.clearCache(sessionId);

    if (success) {
      return res.json({ success: true, message: 'Conversation history cleared' });
    } else {
      return res.status(500).json({ error: 'Failed to clear conversation history' });
    }
  } catch (error) {
    console.error('Error clearing conversation history:', error);
    return res.status(500).json({ error: 'Failed to clear conversation history' });
  }
});

// Language detection and greeting endpoint
app.post('/api/detect-language', (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required for language detection' });
    }

    const detectedLanguage = languageService.detectLanguage(text);
    const greeting = languageService.getGreeting(detectedLanguage);

    res.json({
      detectedLanguage,
      greeting,
      supportedLanguages: ['english', 'french', 'pidgin']
    });
  } catch (error) {
    console.error('Error in language detection:', error);
    res.status(500).json({ error: 'Failed to detect language' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Qala-Lwazi Medical Assistant API is running',
    features: {
      multiLanguage: true,
      conversationMemory: true,
      womensHealthMode: true,
      enhancedRAG: true,
      langchainIntegration: true,
      voiceAssistant: true,
      speechToText: true,
      textToSpeech: true,
      geminiNativeAudio: true
    },
    voiceCapabilities: {
      supportedLanguages: ['english', 'french', 'pidgin'],
      audioFormats: ['wav', 'mp3', 'ogg', 'flac'],
      realTimeStreaming: false, // Will be true after Phase 4
      medicalPronunciation: true
    }
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Qala-Lwazi Medical Assistant API running on port ${port}`);
});
