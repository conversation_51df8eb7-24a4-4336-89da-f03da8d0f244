{"name": "gemini-medical-chatbot-api", "version": "1.0.0", "description": "Backend proxy for Gemini Medical Chatbot", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@google/genai": "^1.0.1", "@google/generative-ai": "^0.24.1", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.64", "@langchain/google-genai": "^0.2.15", "@langchain/pinecone": "^0.2.0", "@pinecone-database/pinecone": "^6.0.0", "@supabase/supabase-js": "^2.49.4", "@xenova/transformers": "^2.17.2", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "franc": "^6.2.0", "langchain": "^0.3.30", "langdetect": "^0.2.1", "multer": "^2.0.0", "pdf-parse": "^1.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}