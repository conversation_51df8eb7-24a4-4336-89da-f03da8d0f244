// Women's Health Specialized Service
// Provides specialized support for women's health questions and concerns

class WomensHealthService {
  constructor() {
    // Women's health topics and keywords
    this.womensHealthTopics = {
      reproductive: [
        'menstruation', 'period', 'menstrual', 'cycle', 'ovulation', 'fertility',
        'pregnancy', 'pregnant', 'conception', 'contraception', 'birth control',
        'pms', 'pmdd', 'endometriosis', 'pcos', 'fibroids', 'ovarian cysts'
      ],
      gynecological: [
        'vaginal', 'cervical', 'uterine', 'ovarian', 'pap smear', 'mammogram',
        'breast', 'gynecologist', 'gynecology', 'pelvic', 'discharge', 'infection',
        'yeast infection', 'uti', 'urinary tract', 'bladder'
      ],
      hormonal: [
        'hormones', 'estrogen', 'progesterone', 'testosterone', 'thyroid',
        'menopause', 'perimenopause', 'hot flashes', 'mood swings', 'hormone therapy'
      ],
      maternal: [
        'pregnancy', 'prenatal', 'postpartum', 'breastfeeding', 'lactation',
        'morning sickness', 'gestational diabetes', 'preeclampsia', 'delivery',
        'cesarean', 'c-section', 'epidural', 'labor'
      ],
      mental_health: [
        'postpartum depression', 'anxiety', 'body image', 'eating disorders',
        'domestic violence', 'sexual health', 'self-care', 'stress management'
      ]
    };

    // Age-specific considerations
    this.ageGroups = {
      adolescent: { min: 13, max: 19, topics: ['puberty', 'first period', 'body changes', 'sexual education'] },
      reproductive: { min: 20, max: 39, topics: ['fertility', 'pregnancy', 'contraception', 'career balance'] },
      perimenopause: { min: 40, max: 55, topics: ['hormone changes', 'irregular periods', 'fertility decline'] },
      menopause: { min: 55, max: 70, topics: ['menopause', 'bone health', 'heart health', 'hormone therapy'] },
      senior: { min: 70, max: 100, topics: ['osteoporosis', 'heart disease', 'cognitive health', 'mobility'] }
    };

    // Cultural considerations for African women
    this.culturalConsiderations = {
      cameroon: {
        commonConcerns: [
          'traditional medicine vs modern medicine',
          'family planning in cultural context',
          'maternal mortality awareness',
          'access to healthcare',
          'nutrition during pregnancy',
          'female genital cutting concerns'
        ],
        supportiveApproach: 'culturally sensitive, respectful of traditional practices while promoting health'
      },
      general_african: {
        commonConcerns: [
          'maternal health',
          'family planning',
          'nutrition',
          'infectious diseases',
          'access to healthcare',
          'economic factors affecting health'
        ],
        supportiveApproach: 'understanding of resource constraints and cultural context'
      }
    };
  }

  // Detect if a query is related to women's health
  isWomensHealthQuery(message) {
    if (!message || typeof message !== 'string') {
      return false;
    }

    const text = message.toLowerCase();
    
    // Check all women's health topic categories
    for (const [category, keywords] of Object.entries(this.womensHealthTopics)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return { isWomensHealth: true, category, confidence: 'high' };
      }
    }

    // Check for general women's health indicators
    const generalIndicators = [
      'women', 'female', 'lady', 'girl', 'mother', 'pregnancy', 'feminine'
    ];

    if (generalIndicators.some(indicator => text.includes(indicator))) {
      return { isWomensHealth: true, category: 'general', confidence: 'medium' };
    }

    return { isWomensHealth: false, category: null, confidence: 'low' };
  }

  // Get specialized women's health context
  getWomensHealthContext(message, conversationHistory = []) {
    const detection = this.isWomensHealthQuery(message);
    
    if (!detection.isWomensHealth) {
      return null;
    }

    const context = {
      category: detection.category,
      confidence: detection.confidence,
      specializedGuidance: this.getSpecializedGuidance(detection.category),
      culturalConsiderations: this.getCulturalGuidance(),
      ageConsiderations: this.getAgeConsiderations(message),
      supportiveLanguage: this.getSupportiveLanguage(),
      resources: this.getRelevantResources(detection.category)
    };

    return context;
  }

  // Get specialized guidance based on category
  getSpecializedGuidance(category) {
    const guidance = {
      reproductive: {
        approach: 'Provide comprehensive, non-judgmental information about reproductive health',
        keyPoints: [
          'Normalize menstrual health discussions',
          'Provide accurate contraception information',
          'Address fertility concerns with sensitivity',
          'Emphasize the importance of regular gynecological care'
        ]
      },
      gynecological: {
        approach: 'Offer clear, medically accurate information while being sensitive to privacy concerns',
        keyPoints: [
          'Encourage regular screenings and check-ups',
          'Provide information about normal vs concerning symptoms',
          'Address common infections and treatments',
          'Emphasize the importance of sexual health'
        ]
      },
      hormonal: {
        approach: 'Explain hormonal changes in an understandable way',
        keyPoints: [
          'Normalize hormonal fluctuations',
          'Provide information about life stage transitions',
          'Discuss treatment options for hormonal imbalances',
          'Address mood and physical symptoms'
        ]
      },
      maternal: {
        approach: 'Provide supportive, evidence-based maternal health information',
        keyPoints: [
          'Support throughout pregnancy journey',
          'Address common pregnancy concerns',
          'Provide postpartum support information',
          'Emphasize prenatal and postnatal care'
        ]
      },
      mental_health: {
        approach: 'Offer compassionate, understanding support for mental health concerns',
        keyPoints: [
          'Validate emotional experiences',
          'Provide information about common mental health issues',
          'Encourage professional mental health support',
          'Address stigma around mental health'
        ]
      },
      general: {
        approach: 'Provide comprehensive women\'s health information',
        keyPoints: [
          'Address health concerns specific to women',
          'Provide preventive care information',
          'Encourage regular health screenings',
          'Support overall wellness and self-care'
        ]
      }
    };

    return guidance[category] || guidance.general;
  }

  // Get cultural guidance
  getCulturalGuidance() {
    return {
      approach: 'Be respectful of cultural backgrounds and traditional practices',
      considerations: [
        'Acknowledge cultural health practices',
        'Provide information that complements traditional knowledge',
        'Be sensitive to family and community influences on health decisions',
        'Address potential barriers to healthcare access',
        'Respect privacy and confidentiality concerns'
      ]
    };
  }

  // Get age-specific considerations
  getAgeConsiderations(message) {
    // This is a simplified age detection - in a real system, you might ask for age
    const text = message.toLowerCase();
    
    if (text.includes('teenager') || text.includes('adolescent') || text.includes('first period')) {
      return this.ageGroups.adolescent;
    } else if (text.includes('pregnancy') || text.includes('trying to conceive')) {
      return this.ageGroups.reproductive;
    } else if (text.includes('menopause') || text.includes('hot flashes')) {
      return this.ageGroups.menopause;
    }
    
    return null; // No specific age group detected
  }

  // Get supportive language guidelines
  getSupportiveLanguage() {
    return {
      tone: 'compassionate, non-judgmental, and empowering',
      guidelines: [
        'Use inclusive and respectful language',
        'Avoid medical jargon when possible',
        'Provide reassurance while being medically accurate',
        'Encourage questions and open communication',
        'Validate concerns and experiences',
        'Emphasize that seeking help is a sign of strength'
      ]
    };
  }

  // Get relevant resources based on category
  getRelevantResources(category) {
    const resources = {
      reproductive: [
        'Planned Parenthood resources',
        'Reproductive health clinics',
        'Fertility specialists',
        'Gynecological care providers'
      ],
      gynecological: [
        'Gynecologists and women\'s health specialists',
        'Women\'s health clinics',
        'Sexual health resources',
        'Screening and prevention programs'
      ],
      hormonal: [
        'Endocrinologists',
        'Women\'s health specialists',
        'Hormone therapy specialists',
        'Menopause support groups'
      ],
      maternal: [
        'Obstetricians and midwives',
        'Prenatal care providers',
        'Lactation consultants',
        'Postpartum support services'
      ],
      mental_health: [
        'Mental health professionals',
        'Women\'s mental health specialists',
        'Support groups',
        'Crisis intervention services'
      ]
    };

    return resources[category] || [];
  }

  // Generate women's health specific prompt additions
  generateWomensHealthPrompt(context, language = 'english') {
    if (!context) {
      return '';
    }

    const prompts = {
      english: `
**Women's Health Specialization Context:**
- Category: ${context.category}
- Approach: ${context.specializedGuidance.approach}
- Key considerations: ${context.specializedGuidance.keyPoints.join(', ')}
- Cultural sensitivity: ${context.culturalConsiderations.approach}
- Supportive tone: ${context.supportiveLanguage.tone}

Please provide responses that are:
1. Medically accurate and evidence-based
2. Culturally sensitive and respectful
3. Supportive and non-judgmental
4. Appropriate for women's health concerns
5. Encouraging of professional healthcare consultation when needed
`,
      french: `
**Contexte de spécialisation en santé des femmes:**
- Catégorie: ${context.category}
- Approche: ${context.specializedGuidance.approach}
- Considérations clés: ${context.specializedGuidance.keyPoints.join(', ')}
- Sensibilité culturelle: ${context.culturalConsiderations.approach}
- Ton de soutien: ${context.supportiveLanguage.tone}

Veuillez fournir des réponses qui sont:
1. Médicalement précises et fondées sur des preuves
2. Culturellement sensibles et respectueuses
3. Soutenantes et sans jugement
4. Appropriées pour les préoccupations de santé des femmes
5. Encourageantes de consultation professionnelle de santé quand nécessaire
`,
      pidgin: `
**Women Health Specialization Context:**
- Category: ${context.category}
- Approach: ${context.specializedGuidance.approach}
- Key considerations: ${context.specializedGuidance.keyPoints.join(', ')}
- Cultural sensitivity: ${context.culturalConsiderations.approach}
- Supportive tone: ${context.supportiveLanguage.tone}

Please provide responses wey be:
1. Medically correct and based on evidence
2. Culturally sensitive and respectful
3. Supportive and no dey judge
4. Appropriate for women health concerns
5. Dey encourage professional healthcare consultation when e necessary
`
    };

    return prompts[language] || prompts.english;
  }

  // Check if conversation should switch to women's health mode
  shouldActivateWomensHealthMode(message, conversationHistory = []) {
    const detection = this.isWomensHealthQuery(message);
    
    if (detection.isWomensHealth && detection.confidence === 'high') {
      return true;
    }

    // Check conversation history for women's health context
    const recentMessages = conversationHistory.slice(-5);
    const womensHealthCount = recentMessages.filter(msg => {
      if (msg.parts && msg.parts[0] && msg.parts[0].text) {
        return this.isWomensHealthQuery(msg.parts[0].text).isWomensHealth;
      }
      return false;
    }).length;

    return womensHealthCount >= 2; // Activate if 2+ recent messages are women's health related
  }
}

module.exports = new WomensHealthService();
