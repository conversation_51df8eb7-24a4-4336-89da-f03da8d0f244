#!/bin/bash

# Qala-Lwazi Backend Deployment Script
# This script builds and deploys the Qala-Lwazi Medical Assistant Backend to Docker Hub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_USERNAME="alphafrederic94"
IMAGE_NAME="qala-lwazi-backend"
VERSION=${1:-latest}

echo -e "${BLUE}🏥 Qala-Lwazi Medical Assistant Backend Deployment${NC}"
echo -e "${BLUE}=================================================${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if logged into Docker Hub
if ! docker info | grep -q "Username"; then
    echo -e "${YELLOW}⚠️  Not logged into Docker Hub. Please login:${NC}"
    docker login
fi

echo -e "${BLUE}📦 Building Docker image...${NC}"
docker build -t ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION} .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully!${NC}"
else
    echo -e "${RED}❌ Docker build failed!${NC}"
    exit 1
fi

echo -e "${BLUE}🚀 Pushing to Docker Hub...${NC}"
docker push ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Successfully pushed to Docker Hub!${NC}"
    echo -e "${GREEN}🎉 Deployment complete!${NC}"
    echo ""
    echo -e "${BLUE}📋 Deployment Information:${NC}"
    echo -e "   Image: ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    echo -e "   Docker Hub: https://hub.docker.com/r/${DOCKER_USERNAME}/${IMAGE_NAME}"
    echo ""
    echo -e "${BLUE}🔧 To run the container:${NC}"
    echo -e "   docker run -p 3001:3001 --env-file .env ${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"
    echo ""
    echo -e "${BLUE}🐳 To run with Docker Compose:${NC}"
    echo -e "   docker-compose up -d"
else
    echo -e "${RED}❌ Failed to push to Docker Hub!${NC}"
    exit 1
fi
